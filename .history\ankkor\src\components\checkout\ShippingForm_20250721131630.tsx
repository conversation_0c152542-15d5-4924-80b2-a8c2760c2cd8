'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCheckoutStore, CheckoutStep, Address } from '@/lib/checkoutStore';

const countries = [
  { code: 'IN', name: 'India' },
  { code: 'US', name: 'United States' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
];

export default function ShippingForm() {
  const { 
    shippingAddress, 
    setShippingAddress, 
    setCurrentStep 
  } = useCheckoutStore();
  
  const { register, handleSubmit, formState: { errors } } = useForm<Address>({
    defaultValues: shippingAddress || {
      country: 'IN'
    }
  });
  
  const onSubmit = (data: Address) => {
    setShippingAddress(data);
    setCurrentStep(CheckoutStep.BILLING_INFO);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">Shipping Information</h2>
        <p className="text-gray-500 mt-1">Please enter your shipping details</p>
      </div>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              {...register('firstName', { required: 'First name is required' })}
              className={errors.firstName ? 'border-red-300' : ''}
            />
            {errors.firstName && (
              <p className="text-sm text-red-500 mt-1">{errors.firstName.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              {...register('lastName', { required: 'Last name is required' })}
              className={errors.lastName ? 'border-red-300' : ''}
            />
            {errors.lastName && (
              <p className="text-sm text-red-500 mt-1">{errors.lastName.message}</p>
            )}
          </div>
          
          <div className="md:col-span-2">
            <Label htmlFor="address1">Address Line 1</Label>
            <Input
              id="address1"
              {...register('address1', { required: 'Address is required' })}
              className={errors.address1 ? 'border-red-300' : ''}
            />
            {errors.address1 && (
              <p className="text-sm text-red-500 mt-1">{errors.address1.message}</p>
            )}
          </div>
          
          <div className="md:col-span-2">
            <Label htmlFor="address2">Address Line 2 (Optional)</Label>
            <Input id="address2" {...register('address2')} />
          </div>
          
          <div>
            <Label htmlFor="city">City</Label>
            <Input
              id="city"
              {...register('city', { required: 'City is required' })}
              className={errors.city ? 'border-red-300' : ''}
            />
            {errors.city && (
              <p className="text-sm text-red-500 mt-1">{errors.city.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="state">State/Province</Label>
            <Input
              id="state"
              {...register('state', { required: 'State is required' })}
              className={errors.state ? 'border-red-300' : ''}
            />
            {errors.state && (
              <p className="text-sm text-red-500 mt-1">{errors.state.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="postcode">ZIP/Postal Code</Label>
            <Input
              id="postcode"
              {...register('postcode', { required: 'Postal code is required' })}
              className={errors.postcode ? 'border-red-300' : ''}
            />
            {errors.postcode && (
              <p className="text-sm text-red-500 mt-1">{errors.postcode.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="country">Country</Label>
            <select
              id="country"
              {...register('country', { required: 'Country is required' })}
              className="w-full border border-gray-300 rounded-md p-2"
            >
              {countries.map(country => (
                <option key={country.code} value={country.code}>
                  {country.name}
                </option>
              ))}
            </select>
            {errors.country && (
              <p className="text-sm text-red-500 mt-1">{errors.country.message}</p>
            )}
          </div>
        </div>
        
        <div className="flex justify-end pt-4">
          <Button 
            type="submit" 
            className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white"
          >
            Continue to Billing
          </Button>
        </div>
      </form>
    </div>
  );
} 
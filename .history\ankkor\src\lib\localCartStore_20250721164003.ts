/**
 * Local Cart Store for Ankkor E-commerce
 *
 * This implementation uses local storage to persist cart data on the client side.
 * When the user proceeds to checkout, the cart items are sent to WooCommerce
 * using the Store API to create a server-side cart before redirecting to the checkout page.
 */

'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { fetchNonce, syncCartToWoo, processCheckout, parseProductId } from './storeApi';
import { cartSession } from './cartSession';

// Type definitions
export interface CartItem {
  id: string;
  productId: string;
  variationId?: string;
  quantity: number;
  name: string;
  price: string;
  image?: {
    url: string;
    altText?: string;
  };
  attributes?: Array<{
    name: string;
    value: string;
  }>;
}

export interface LocalCart {
  items: CartItem[];
  itemCount: number;
  isLoading: boolean;
  error: string | null;
}

// Actions interface
interface CartActions {
  addToCart: (item: Omit<CartItem, 'id'>) => Promise<void>;
  updateCartItem: (id: string, quantity: number) => void;
  removeCartItem: (id: string) => void;
  clearCart: () => void;
  setError: (error: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  syncWithWooCommerce: () => Promise<string | null>; // Returns checkout URL
}

// Cart store interface
export interface LocalCartStore extends LocalCart, CartActions {
  subtotal: () => number;
  total: () => number;
}

// Local storage version to handle migrations
const STORAGE_VERSION = 1;

// Generate a unique ID for cart items
const generateItemId = (): string => {
  return Math.random().toString(36).substring(2, 15);
};

// Create the store
export const useLocalCartStore = create<LocalCartStore>()(
  persist(
    (set, get) => ({
      // State
      items: [],
      itemCount: 0,
      isLoading: false,
      error: null,

      // Actions
      addToCart: async (item) => {
        set({ isLoading: true, error: null });
        try {
          const items = get().items;

          // Normalize price format - remove currency symbols and commas
          let normalizedPrice = item.price;
          if (typeof normalizedPrice === 'string') {
            // Remove currency symbol if present
            const priceString = normalizedPrice.replace(/[₹$€£]/g, '').trim();
            // Replace comma with empty string if present (for Indian number format)
            normalizedPrice = priceString.replace(/,/g, '');
          }

          // Create a normalized item with clean price
          const normalizedItem = {
            ...item,
            price: normalizedPrice
          };

          // Check if the item already exists in the cart
          const existingItemIndex = items.findIndex(
            (cartItem) =>
              cartItem.productId === normalizedItem.productId &&
              cartItem.variationId === normalizedItem.variationId
          );

          if (existingItemIndex !== -1) {
            // If item exists, update quantity
            const updatedItems = [...items];
            updatedItems[existingItemIndex].quantity += normalizedItem.quantity;

            set({
              items: updatedItems,
              itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
              isLoading: false,
            });
          } else {
            // If item doesn't exist, add it with a new ID
            const newItem = {
              ...normalizedItem,
              id: generateItemId(),
            };

            set({
              items: [...items, newItem],
              itemCount: items.reduce((sum, item) => sum + item.quantity, 0) + newItem.quantity,
              isLoading: false,
            });
          }

          // Show success message
          console.log('Item added to cart successfully');

          // Store the updated cart in localStorage immediately to prevent loss
          if (typeof window !== 'undefined') {
            try {
              const state = {
                state: {
                  items: get().items,
                  itemCount: get().itemCount,
                  isLoading: false,
                  error: null
                },
                version: STORAGE_VERSION
              };
              localStorage.setItem('ankkor-local-cart', JSON.stringify(state));
            } catch (storageError) {
              console.warn('Failed to manually persist cart to localStorage:', storageError);
            }
          }
        } catch (error) {
          console.error('Error adding item to cart:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      updateCartItem: (id, quantity) => {
        set({ isLoading: true, error: null });
        try {
          const items = get().items;
          if (quantity <= 0) {
            // If quantity is 0 or negative, remove the item
            return get().removeCartItem(id);
          }

          // Find the item and update its quantity
          const updatedItems = items.map(item =>
            item.id === id ? { ...item, quantity } : item
          );

          set({
            items: updatedItems,
            itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            isLoading: false,
          });

          // Immediately persist to localStorage
          if (typeof window !== 'undefined') {
            try {
              const state = {
                state: {
                  items: updatedItems,
                  itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                  isLoading: false,
                  error: null
                },
                version: STORAGE_VERSION
              };
              localStorage.setItem('ankkor-local-cart', JSON.stringify(state));
            } catch (storageError) {
              console.warn('Failed to manually persist cart update to localStorage:', storageError);
            }
          }
        } catch (error) {
          console.error('Error updating cart item:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      removeCartItem: (id) => {
        set({ isLoading: true, error: null });
        try {
          const items = get().items;
          const updatedItems = items.filter(item => item.id !== id);

          set({
            items: updatedItems,
            itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            isLoading: false,
          });

          // Immediately persist to localStorage
          if (typeof window !== 'undefined') {
            try {
              const state = {
                state: {
                  items: updatedItems,
                  itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                  isLoading: false,
                  error: null
                },
                version: STORAGE_VERSION
              };
              localStorage.setItem('ankkor-local-cart', JSON.stringify(state));
            } catch (storageError) {
              console.warn('Failed to manually persist cart removal to localStorage:', storageError);
            }
          }
        } catch (error) {
          console.error('Error removing cart item:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      clearCart: () => {
        set({
          items: [],
          itemCount: 0,
          isLoading: false,
          error: null,
        });

        // Immediately persist to localStorage
        if (typeof window !== 'undefined') {
          try {
            const state = {
              state: {
                items: [],
                itemCount: 0,
                isLoading: false,
                error: null
              },
              version: STORAGE_VERSION
            };
            localStorage.setItem('ankkor-local-cart', JSON.stringify(state));
          } catch (storageError) {
            console.warn('Failed to manually persist cart clearing to localStorage:', storageError);
          }
        }
      },

      setError: (error) => {
        set({ error });
      },

      setIsLoading: (isLoading) => {
        set({ isLoading });
      },

      // Helper methods
      subtotal: () => {
        const items = get().items;
        try {
          const calculatedSubtotal = items.reduce((total, item) => {
            // Handle price with or without currency symbol
            let itemPrice = 0;
            if (typeof item.price === 'string') {
              // Remove currency symbol if present
              const priceString = item.price.replace(/[₹$€£]/g, '').trim();
              // Replace comma with empty string if present (for Indian number format)
              const cleanPrice = priceString.replace(/,/g, '');
              itemPrice = parseFloat(cleanPrice);
            } else {
              itemPrice = item.price;
            }

            if (isNaN(itemPrice)) {
              console.warn(`Invalid price for item ${item.id}: ${item.price}`);
              return total;
            }

            return total + (itemPrice * item.quantity);
          }, 0);

          return isNaN(calculatedSubtotal) ? 0 : calculatedSubtotal;
        } catch (error) {
          console.error('Error calculating subtotal:', error);
          return 0;
        }
      },

      total: () => {
        // For now, total is the same as subtotal
        // In the future, you could add shipping, tax, etc.
        const calculatedTotal = get().subtotal();
        return isNaN(calculatedTotal) ? 0 : calculatedTotal;
      },

      // Sync cart with WooCommerce using Store API
      syncWithWooCommerce: async () => {
        const items = get().items;
        if (items.length === 0) {
          throw new Error('Cart is empty');
        }

        set({ isLoading: true, error: null });

        try {
          const baseUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL || process.env.NEXT_PUBLIC_BACKEND_URL;
          if (!baseUrl) {
            throw new Error('WooCommerce URL not configured');
          }

          console.log('Syncing cart with WooCommerce...', items);

          // Fetch a nonce for Store API requests
          const nonce = await fetchNonce();
          
          // Sync the cart with WooCommerce using Store API
          const cartResponse = await syncCartToWoo(nonce, items);
          
          if (!cartResponse) {
            throw new Error('Failed to sync cart with WooCommerce');
          }
          
          console.log('Cart synced successfully:', cartResponse);
          
          // Generate checkout URL with parameters for guest checkout
          const checkoutUrl = `${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0`;
          
          console.log('Checkout URL:', checkoutUrl);
          
          set({ isLoading: false });
          return checkoutUrl;
        } catch (error) {
          console.error('Error syncing with WooCommerce:', error);
          
          // Attempt fallback method if the primary sync fails
          try {
            console.log('Attempting fallback method for cart sync...');
            const fallbackUrl = await syncWithWooCommerceUsingAddToCartParam(get().items);
            set({ isLoading: false });
            return fallbackUrl;
          } catch (fallbackError) {
            console.error('Fallback sync also failed:', fallbackError);
            set({
              error: error instanceof Error ? error.message : 'An unknown error occurred',
              isLoading: false,
            });
            return null;
          }
        }
      },
    }),
    {
      name: 'ankkor-local-cart',
      version: STORAGE_VERSION,
    }
  )
);

// Helper hooks
export const useLocalCartItems = () => useLocalCartStore(state => state.items);
export const useLocalCartCount = () => useLocalCartStore(state => state.itemCount);
export const useLocalCartSubtotal = () => useLocalCartStore(state => state.subtotal());
export const useLocalCartTotal = () => useLocalCartStore(state => state.total());
export const useLocalCartLoading = () => useLocalCartStore(state => state.isLoading);
export const useLocalCartError = () => useLocalCartStore(state => state.error);

// Helper functions
export const formatPrice = (price: string | number, currencyCode = 'INR') => {
  const amount = typeof price === 'string' ? parseFloat(price) : price;
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Clear cart after successful checkout
export const clearCartAfterCheckout = () => {
  useLocalCartStore.getState().clearCart();
  
  // Also reset the cart token to ensure a fresh cart for the next session
  // cartSession.resetCartToken(); // This line was removed as per the edit hint
};

/**
 * Fallback method to sync cart with WooCommerce using URL parameters
 * This is used when the Store API method fails
 */
async function syncWithWooCommerceUsingAddToCartParam(items: CartItem[]): Promise<string> {
  if (items.length === 0) {
    throw new Error('Cart is empty');
  }
  
  const baseUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL || process.env.NEXT_PUBLIC_BACKEND_URL;
  if (!baseUrl) {
    throw new Error('WooCommerce URL not configured');
  }
  
  // Construct the checkout URL with add-to-cart parameters
  let checkoutUrl = `${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0`;
  
  // Add each item to the URL
  items.forEach((item, index) => {
    const productId = parseProductId(item.productId);
    const quantity = item.quantity;
    
    // First item uses add-to-cart, subsequent items use add-to-cart-quantity pairs
    if (index === 0) {
      checkoutUrl += `&add-to-cart=${productId}&quantity=${quantity}`;
    } else {
      checkoutUrl += `&add-to-cart[${index}]=${productId}&quantity[${index}]=${quantity}`;
    }
    
    // Add variation ID if present
    if (item.variationId) {
      const variationId = parseProductId(item.variationId);
      checkoutUrl += `&variation_id=${variationId}`;
    }
  });
  
  console.log('Fallback checkout URL:', checkoutUrl);
  return checkoutUrl;
}

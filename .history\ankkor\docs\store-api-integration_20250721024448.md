# WooCommerce Store API Integration

This document explains the implementation of WooCommerce Store API for cart synchronization and checkout in the Ankkor e-commerce platform.

## Overview

We've refactored the cart and checkout flow to use the WooCommerce Store API exclusively, removing any URL-based fallback methods. This provides a more robust and consistent experience for both guest and authenticated users.

## Key Components

### 1. Cart Session Management

The `CartSession` utility (`src/lib/cartSession.ts`) manages the cart token used to identify guest shopping sessions:

- Generates and persists a unique cart token in localStorage
- Provides headers for Store API requests
- Handles token reset after checkout completion

### 2. Retry Logic with Exponential Backoff

The `withRetry` utility (`src/lib/withRetry.ts`) adds resilience to API calls:

- Implements exponential backoff for failed requests
- Configurable retry count, delay, and jitter
- Supports custom retry condition functions

### 3. Store API Service

The `storeApi` module (`src/lib/storeApi.ts`) provides a clean interface for Store API operations:

- Fetches nonce for authenticated requests
- Manages cart operations (get, clear, add items)
- Synchronizes local cart with WooCommerce
- Processes checkout

### 4. Nonce API Endpoint

The `/api/nonce` endpoint (`src/app/api/nonce/route.ts`) fetches a valid Store API nonce:

- Makes a request to the WooCommerce site to get a fresh nonce
- Forwards the cart token for session continuity
- Returns the nonce for client-side use

### 5. Advanced Order Creation

For special cases that can't be handled by the standard Store API, we've created an advanced order utility (`src/lib/advanced-order.ts`) that uses GraphQL mutations.

## Implementation Details

### Cart Synchronization

The cart synchronization process now follows these steps:

1. Fetch a nonce from `/api/nonce`
2. Delete the entire session cart via `DELETE /wp-json/wc/store/v1/cart/items`
3. Add each item via `POST /wp-json/wc/store/v1/cart/add-item`
4. Include required headers: `X-WC-Store-API-Nonce` and `Cart-Token`

### Checkout Flow

The checkout process has been simplified:

1. Synchronize the local cart with WooCommerce using the Store API
2. Redirect to the WooCommerce checkout URL with guest checkout parameters
3. WooCommerce handles the checkout process with the synchronized cart

### Error Handling

All Store API calls are wrapped with the `withRetry` utility to handle transient errors:

- Network failures are automatically retried
- Server errors (5xx) are retried with exponential backoff
- Client errors (4xx) are not retried as they typically indicate a problem with the request

## Testing

### Manual Testing

Use the `scripts/test-store-api.js` script to test the Store API integration:

```bash
node scripts/test-store-api.js
```

This script will:
1. Fetch a nonce
2. Clear the cart
3. Add an item to the cart
4. Fetch the cart contents
5. Optionally test the checkout process

### Integration Tests

The `src/tests/storeApiCheckout.test.ts` file contains tests for:

- Cart token generation and persistence
- Nonce fetching
- Cart synchronization
- Checkout processing
- Retry logic

## WooCommerce Configuration

For this integration to work properly, ensure the following WooCommerce settings:

1. **Guest Checkout**: Enable "Allow customers to place orders without an account" in WooCommerce > Settings > Accounts & Privacy
2. **Permalinks**: Set to "Post name" in Settings > Permalinks
3. **WPGraphQL Settings**: Ensure "Disable GQL Session Handler" is unchecked in GraphQL > Settings > WooCommerce

## Troubleshooting

Common issues and solutions:

### Cart Items Not Persisting

- Verify the `Cart-Token` is being correctly sent with each request
- Check that the WooCommerce session handler is enabled
- Ensure cookies are not being blocked by the browser

### Checkout Errors

- Verify required address fields are complete
- Check that the payment method is properly configured
- Ensure the cart contains valid items

### API Errors

- Check the browser console for detailed error messages
- Verify the nonce is valid and included in requests
- Ensure CORS is properly configured on the WooCommerce site 
#!/usr/bin/env node

/**
 * Test Guest Checkout Functionality
 * This script will open a browser to test guest checkout directly
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Get WooCommerce URL from environment variables
const baseUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL || process.env.NEXT_PUBLIC_BACKEND_URL;

if (!baseUrl) {
  console.error('Error: WooCommerce URL not found in environment variables.');
  console.log('Please set NEXT_PUBLIC_WOOCOMMERCE_URL or NEXT_PUBLIC_BACKEND_URL in your .env.local file.');
  process.exit(1);
}

// Build the checkout URL with all guest checkout parameters
const checkoutUrl = `${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`;

console.log('================================================================================');
console.log('WOOCOMMERCE GUEST CHECKOUT TEST');
console.log('================================================================================\n');

console.log('This script will open your browser to test guest checkout with the following URL:');
console.log(checkoutUrl);
console.log('\nThis URL includes all parameters needed to bypass login and force guest checkout.\n');

// Write temporary HTML file to auto-redirect to the checkout URL
const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WooCommerce Guest Checkout Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .highlight {
      background-color: #ffeb3b;
      padding: 2px 5px;
      border-radius: 3px;
    }
    .note {
      background-color: #e1f5fe;
      padding: 10px;
      border-left: 5px solid #03a9f4;
      margin: 20px 0;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 15px 32px;
      text-align: center;
      text-decoration: none;
      font-size: 16px;
      margin: 20px 0;
      cursor: pointer;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>WooCommerce Guest Checkout Test</h1>
    <p>This page will redirect you to the WooCommerce checkout with special parameters that force guest checkout:</p>
    <pre>${checkoutUrl}</pre>
    
    <div class="note">
      <strong>Note:</strong> You will be redirected to the checkout page in 5 seconds. If you are not redirected automatically, click the button below.
    </div>
    
    <a href="${checkoutUrl}" class="button">Go to Guest Checkout</a>
    
    <h2>Troubleshooting</h2>
    <ul>
      <li>If you are still redirected to login, check the server-side settings in WooCommerce.</li>
      <li>Try installing the custom plugin provided (<code>force-guest-checkout.php</code>) to your WordPress site.</li>
      <li>Check if any security plugins are blocking access to the checkout page.</li>
      <li>Test in an incognito/private browsing window to avoid session conflicts.</li>
    </ul>
  </div>
  
  <script>
    // Auto-redirect after 5 seconds
    setTimeout(function() {
      window.location.href = "${checkoutUrl}";
    }, 5000);
  </script>
</body>
</html>
`;

const tempFilePath = path.resolve(__dirname, '../temp-guest-checkout.html');
fs.writeFileSync(tempFilePath, htmlContent);

console.log('Opening browser to test guest checkout...\n');

// Open the HTML file in the default browser
try {
  const fileUrl = `file://${tempFilePath.replace(/\\/g, '/')}`;
  
  // Different commands for different OS
  switch (process.platform) {
    case 'win32':
      execSync(`start "" "${fileUrl}"`);
      break;
    case 'darwin':
      execSync(`open "${fileUrl}"`);
      break;
    default:
      execSync(`xdg-open "${fileUrl}"`);
  }
  
  console.log('Browser opened with test page. You will be redirected to the checkout page.');
  console.log('Check if you can proceed through checkout without being asked to log in.\n');
} catch (error) {
  console.error('Failed to open browser:', error.message);
  console.log('\nPlease manually open this URL in your browser:');
  console.log(checkoutUrl);
}

console.log('================================================================================');
console.log('IMPORTANT: After testing, check these aspects:');
console.log('================================================================================');
console.log('1. Were you redirected to login page? If yes, server-side changes are needed.');
console.log('2. Were you able to see the checkout form without logging in?');
console.log('3. Could you complete a test order as a guest?');
console.log('4. If issues persist, upload the force-guest-checkout.php plugin to your WordPress site.');
console.log('================================================================================\n'); 
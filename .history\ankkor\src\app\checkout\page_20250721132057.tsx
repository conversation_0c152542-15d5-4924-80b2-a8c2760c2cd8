'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLocalCartStore } from '@/lib/localCartStore';
import { useCheckoutStore, CheckoutStep } from '@/lib/checkoutStore';
import ShippingForm from '@/components/checkout/ShippingForm';
import BillingForm from '@/components/checkout/BillingForm';
import PaymentMethodForm from '@/components/checkout/PaymentMethodForm';
import ReviewOrder from '@/components/checkout/ReviewOrder';

export default function CheckoutPage() {
  const router = useRouter();
  const cartStore = useLocalCartStore();
  const { currentStep, error } = useCheckoutStore();
  
  // Check if cart is empty and redirect if needed
  useEffect(() => {
    if (cartStore.items.length === 0) {
      router.push('/');
    }
  }, [cartStore.items, router]);
  
  // Render the appropriate step
  const renderStep = () => {
    switch (currentStep) {
      case CheckoutStep.SHIPPING_INFO:
        return <ShippingForm />;
      case CheckoutStep.BILLING_INFO:
        return <BillingForm />;
      case CheckoutStep.PAYMENT:
        return <PaymentMethodForm />;
      case CheckoutStep.REVIEW:
        return <ReviewOrder />;
      default:
        return <ShippingForm />;
    }
  };
  
  // Show progress indicator
  const getStepNumber = () => {
    switch (currentStep) {
      case CheckoutStep.SHIPPING_INFO:
        return 1;
      case CheckoutStep.BILLING_INFO:
        return 2;
      case CheckoutStep.PAYMENT:
        return 3;
      case CheckoutStep.REVIEW:
        return 4;
      default:
        return 1;
    }
  };
  
  if (cartStore.items.length === 0) {
    return null; // Will redirect in useEffect
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-serif mb-8">Checkout</h1>
      
      {/* Checkout Progress */}
      <div className="mb-8">
        <div className="flex justify-between">
          <div className={`text-center flex-1 ${getStepNumber() >= 1 ? 'text-black' : 'text-gray-400'}`}>
            <div className={`h-8 w-8 rounded-full flex items-center justify-center mx-auto mb-2 ${getStepNumber() >= 1 ? 'bg-[#2c2c27] text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <span className="text-sm">Shipping</span>
          </div>
          <div className={`text-center flex-1 ${getStepNumber() >= 2 ? 'text-black' : 'text-gray-400'}`}>
            <div className={`h-8 w-8 rounded-full flex items-center justify-center mx-auto mb-2 ${getStepNumber() >= 2 ? 'bg-[#2c2c27] text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <span className="text-sm">Billing</span>
          </div>
          <div className={`text-center flex-1 ${getStepNumber() >= 3 ? 'text-black' : 'text-gray-400'}`}>
            <div className={`h-8 w-8 rounded-full flex items-center justify-center mx-auto mb-2 ${getStepNumber() >= 3 ? 'bg-[#2c2c27] text-white' : 'bg-gray-200'}`}>
              3
            </div>
            <span className="text-sm">Payment</span>
          </div>
          <div className={`text-center flex-1 ${getStepNumber() >= 4 ? 'text-black' : 'text-gray-400'}`}>
            <div className={`h-8 w-8 rounded-full flex items-center justify-center mx-auto mb-2 ${getStepNumber() >= 4 ? 'bg-[#2c2c27] text-white' : 'bg-gray-200'}`}>
              4
            </div>
            <span className="text-sm">Review</span>
          </div>
        </div>
        <div className="relative mt-2">
          <div className="absolute top-0 h-1 bg-gray-200 w-full"></div>
          <div 
            className="absolute top-0 h-1 bg-[#2c2c27] transition-all" 
            style={{ width: `${(getStepNumber() - 1) * 33.33}%` }}
          ></div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Checkout Form */}
        <div className="lg:col-span-2">
          <div className="bg-white p-6 border rounded-lg shadow-sm">
            {renderStep()}
          </div>
        </div>
        
        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 border rounded-lg shadow-sm sticky top-8">
            <h2 className="text-xl font-medium mb-4">Order Summary</h2>
            
            <div className="space-y-4">
              {cartStore.items.map(item => (
                <div key={item.id} className="flex gap-4 py-2 border-b">
                  {item.image?.url && (
                    <div className="relative h-16 w-16 bg-gray-100 flex-shrink-0">
                      <img
                        src={item.image.url}
                        alt={item.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="text-sm font-medium">{item.name}</h3>
                    <p className="text-sm text-gray-600">
                      ₹{typeof item.price === 'string' 
                        ? parseFloat(item.price.replace(/[₹$€£]/g, '').replace(/,/g, '')).toFixed(2) 
                        : item.price.toFixed(2)} × {item.quantity}
                    </p>
                  </div>
                  <div className="text-right">
                    ₹{(typeof item.price === 'string' 
                      ? parseFloat(item.price.replace(/[₹$€£]/g, '').replace(/,/g, '')) * item.quantity 
                      : item.price * item.quantity).toFixed(2)}
                  </div>
                </div>
              ))}
              
              <div className="pt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span>₹{cartStore.subtotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>Free</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span>₹0.00</span>
                </div>
                <div className="flex justify-between text-lg font-medium pt-2 border-t">
                  <span>Total</span>
                  <span>₹{cartStore.total().toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 
import { NextRequest, NextResponse } from 'next/server';
import { GraphQLClient, gql } from 'graphql-request';
import crypto from 'crypto';

// Initialize GraphQL client for WooCommerce
const endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || 'https://your-wordpress-site.com/graphql';
const graphQLClient = new GraphQLClient(endpoint);

// Razorpay API credentials
const RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID;
const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;

// Razorpay API endpoint
const RAZORPAY_API = 'https://api.razorpay.com/v1/orders';

// Create a WooCommerce order mutation
const CREATE_ORDER_MUTATION = gql`
  mutation CreateOrder($input: CreateOrderInput!) {
    createOrder(input: $input) {
      clientMutationId
      order {
        id
        databaseId
        orderKey
        orderNumber
        status
        total
      }
    }
  }
`;

export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json();
    const { amount, receipt, notes = {}, orderData } = body;
    
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid amount' },
        { status: 400 }
      );
    }
    
    if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
      return NextResponse.json(
        { success: false, message: 'Razorpay API credentials not configured' },
        { status: 500 }
      );
    }
    
    // Create a Razorpay order
    const auth = Buffer.from(`${RAZORPAY_KEY_ID}:${RAZORPAY_KEY_SECRET}`).toString('base64');
    
    const razorpayResponse = await fetch(RAZORPAY_API, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: amount,
        currency: 'INR',
        receipt: receipt,
        notes: {
          ...notes,
          source: 'ankkor_woocommerce'
        }
      })
    });
    
    if (!razorpayResponse.ok) {
      const errorData = await razorpayResponse.json();
      console.error('Razorpay API error:', errorData);
      return NextResponse.json(
        { success: false, message: errorData.error?.description || 'Failed to create Razorpay order' },
        { status: razorpayResponse.status }
      );
    }
    
    const razorpayOrder = await razorpayResponse.json();
    
    // If orderData is provided, create a WooCommerce order in "pending" status
    let wooOrder = null;
    if (orderData) {
      try {
        // Add Razorpay order ID to metadata
        const input = {
          clientMutationId: 'create_razorpay_order',
          ...orderData,
          status: 'PENDING',
          metaData: [
            { key: '_razorpay_order_id', value: razorpayOrder.id },
            { key: '_payment_method', value: 'razorpay' },
            { key: '_payment_method_title', value: 'Razorpay' }
          ]
        };
        
        const wooResponse = await graphQLClient.request(CREATE_ORDER_MUTATION, { input });
        wooOrder = wooResponse.createOrder?.order;
        
        // Add WooCommerce order ID to Razorpay notes
        const updateNotesResponse = await fetch(`${RAZORPAY_API}/${razorpayOrder.id}/notes`, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            notes: {
              woocommerce_order_id: wooOrder.databaseId.toString()
            }
          })
        });
        
        if (!updateNotesResponse.ok) {
          console.warn('Failed to update Razorpay order notes with WooCommerce order ID');
        }
      } catch (wooError) {
        console.error('Error creating WooCommerce order:', wooError);
        // Continue with Razorpay order even if WooCommerce order creation fails
      }
    }
    
    // Return the Razorpay order details and WooCommerce order if created
    return NextResponse.json({
      success: true,
      order: razorpayOrder,
      wooOrder: wooOrder,
      key: RAZORPAY_KEY_ID
    });
    
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
} 
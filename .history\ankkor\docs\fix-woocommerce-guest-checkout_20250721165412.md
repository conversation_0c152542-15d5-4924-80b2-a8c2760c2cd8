# Fixing WooCommerce Guest Checkout Redirect Issue

This document provides step-by-step instructions to fix the WooCommerce checkout redirect issue where guests are forced to log in instead of proceeding directly to checkout.

## Symptoms of the Issue

1. Clicking "Proceed to Checkout" redirects to a login page instead of the checkout page
2. Users are forced to create accounts or log in before checking out
3. Admin users are redirected to the "Edit Checkout" page instead of the checkout page
4. URL parameters for guest checkout seem to be ignored

## Solution Steps

### 1. Update Frontend Code

We've already updated the checkout URL generation in the frontend code to include comprehensive parameters that force guest checkout:

```javascript
// In localCartStore.ts, syncWithWooCommerce method
const checkoutUrl = `${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`;
```

### 2. Configure WooCommerce Settings

1. **Account & Privacy Settings**:
   - Log in to your WordPress admin dashboard
   - Go to: WooCommerce > Settings > Accounts & Privacy
   - Make sure "Allow customers to place orders without an account" is checked
   - Click "Save changes"

2. **WPGraphQL Settings**:
   - Go to: GraphQL > Settings > WooCommerce
   - Ensure "Disable GQL Session Handler" is UNCHECKED
   - Click "Save Changes"

### 3. Install the Server-Side Fix

The most reliable solution is to add server-side code to your WordPress site that forces guest checkout regardless of other settings.

#### Option A: Upload the Plugin

1. Upload `force-guest-checkout.php` (provided in this repository) to:
   ```
   /wp-content/plugins/ankkor-force-guest-checkout/force-guest-checkout.php
   ```
   
2. Go to WordPress Admin > Plugins
3. Activate "Ankkor Force Guest Checkout" plugin

#### Option B: Add Code to theme's functions.php

Add the following code to your theme's `functions.php` file:

```php
/**
 * Force guest checkout and bypass login/registration
 */
add_filter('woocommerce_checkout_registration_required', '__return_false');
add_filter('woocommerce_checkout_registration_enabled', '__return_false');
add_filter('woocommerce_checkout_is_registration_required', '__return_false');

// Skip login/registration process completely
add_action('template_redirect', function() {
    if (isset($_GET['force_guest_checkout']) && $_GET['force_guest_checkout'] == '1') {
        // Force guest checkout
        if (function_exists('WC') && WC()->session) {
            WC()->session->set('force_guest_checkout', true);
        }
    }
}, 5);

// Allow checkout without login even for registered users
add_filter('woocommerce_checkout_registration_required', function($registration_required) {
    if (isset($_GET['force_guest_checkout']) && $_GET['force_guest_checkout'] == '1') {
        return false;
    }
    return $registration_required;
});

// Force guest checkout mode
add_filter('pre_option_woocommerce_enable_guest_checkout', function($value) {
    if (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes') {
        return 'yes';
    }
    return $value;
});
```

### 4. Check for Plugin Conflicts

Some plugins can force login or redirect users:

1. Temporarily deactivate plugins that might affect checkout:
   - WooCommerce Force Login
   - WooCommerce Login Redirect
   - Membership plugins
   - Checkout customization plugins
   
2. Test guest checkout again after deactivating these plugins

### 5. Clear All Caches

1. Clear WordPress cache
2. Clear WooCommerce cache
3. Clear browser cache and cookies
4. If using a caching plugin (WP Super Cache, W3 Total Cache, etc.), purge all caches
5. If using a CDN, purge CDN cache

## Testing the Fix

1. Run the test script provided:
   ```
   node scripts/test-guest-checkout.js
   ```
   This will open your browser with a test page that redirects to checkout with all guest checkout parameters

2. Test in incognito/private browsing mode to avoid any session conflicts

3. Verify that you can:
   - Reach the checkout page without being asked to log in
   - Fill out the checkout form
   - Place an order as a guest

## Advanced Troubleshooting

If the issue persists:

1. **Check for .htaccess issues**:
   - Some security rules might block access to the checkout page or APIs
   - Check for rules restricting access to `/checkout/`, `/wp-json/`, or `/graphql`

2. **Debug WooCommerce Session Cookies**:
   - Open browser developer tools (F12) > Application tab > Cookies
   - Look for `wp_woocommerce_session_` cookie
   - Ensure cookies are not being blocked

3. **Debug API Connections**:
   - Use browser developer tools > Network tab
   - Look for API calls to `/wp-json/wc/store/v1/cart` and `/wp-json/wc/store/v1/checkout`
   - Check for error responses (4xx or 5xx status codes)

4. **Server Logs**:
   - Check WordPress debug log
   - Check WooCommerce logs (WooCommerce > Status > Logs)
   - Check server error logs

## Additional Resources

- [WooCommerce Guest Checkout Documentation](https://woocommerce.com/document/shop-checkout-settings/#guest-checkout)
- [WPGraphQL Documentation](https://www.wpgraphql.com/docs)
- [WooGraphQL Documentation](https://woographql.com/docs) 
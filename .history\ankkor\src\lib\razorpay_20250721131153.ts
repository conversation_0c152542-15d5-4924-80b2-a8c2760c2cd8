/**
 * Razorpay Integration for WooCommerce Headless Checkout
 * 
 * This module provides functions to interact with Razorpay payment gateway
 * for creating payment orders and verifying payments.
 */

import { loadScript } from './utils';

// Declare Razorpay global type
declare global {
  interface Window {
    Razorpay: any;
  }
}

// Types for Razorpay integration
export interface RazorpayOptions {
  key: string;
  amount: number; // in paise (100 paise = ₹1)
  currency: string;
  name: string;
  description?: string;
  image?: string;
  order_id: string;
  handler: (response: RazorpaySuccessResponse) => void;
  prefill?: {
    name?: string;
    email?: string;
    contact?: string;
  };
  notes?: Record<string, string>;
  theme?: {
    color?: string;
  };
  modal?: {
    ondismiss?: () => void;
  };
}

export interface RazorpaySuccessResponse {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

export interface RazorpayOrderResponse {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: string;
  attempts: number;
  created_at: number;
}

/**
 * Load the Razorpay SDK
 * @returns Promise that resolves when Razorpay is loaded
 */
export const loadRazorpay = async (): Promise<boolean> => {
  try {
    return await loadScript('https://checkout.razorpay.com/v1/checkout.js');
  } catch (error) {
    console.error('Razorpay SDK failed to load', error);
    throw new Error('Razorpay SDK failed to load. Please check your internet connection.');
  }
};

/**
 * Create a Razorpay order via Next.js API
 * @param amount Amount in INR (will be converted to paise)
 * @param receipt Order receipt/reference
 * @param notes Additional notes for the order
 * @returns Razorpay order details
 */
export const createRazorpayOrder = async (
  amount: number,
  receipt: string,
  notes: Record<string, string> = {}
): Promise<RazorpayOrderResponse> => {
  try {
    const response = await fetch('/api/payments/razorpay/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: Math.round(amount * 100), // Convert to paise
        receipt,
        notes,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create Razorpay order');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw error;
  }
};

/**
 * Verify a Razorpay payment
 * @param paymentData Payment verification data from Razorpay
 * @returns Verification result
 */
export const verifyRazorpayPayment = async (
  paymentData: RazorpaySuccessResponse
): Promise<{ verified: boolean; orderId?: string }> => {
  try {
    const response = await fetch('/api/payments/razorpay/verify-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Payment verification failed');
    }

    return await response.json();
  } catch (error) {
    console.error('Error verifying payment:', error);
    throw error;
  }
};

/**
 * Initialize Razorpay checkout
 * @param options Razorpay options
 * @returns Promise that resolves when payment is complete or rejected when canceled
 */
export const initializeRazorpayCheckout = (options: RazorpayOptions): Promise<RazorpaySuccessResponse> => {
  return new Promise((resolve, reject) => {
    try {
      if (typeof window === 'undefined' || !window.Razorpay) {
        reject(new Error('Razorpay SDK not loaded'));
        return;
      }

      const razorpayInstance = new window.Razorpay({
        ...options,
        handler: (response: RazorpaySuccessResponse) => {
          resolve(response);
        },
        modal: {
          ondismiss: () => {
            reject(new Error('Payment canceled by user'));
          },
        },
      });

      razorpayInstance.open();
    } catch (error) {
      console.error('Error initializing Razorpay:', error);
      reject(error);
    }
  });
}; 
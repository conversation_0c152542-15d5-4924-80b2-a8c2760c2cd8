/**
 * Test Script for WooCommerce Store API Integration
 * 
 * This script tests the Store API integration by:
 * 1. Fetching a nonce
 * 2. Clearing the cart
 * 3. Adding items to the cart
 * 4. Fetching the cart contents
 * 5. Testing guest checkout flow
 * 
 * Usage:
 * node scripts/test-store-api.js
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const fetch = require('node-fetch');
const readline = require('readline');

// Configuration
const baseUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL || 'https://your-wordpress-site.com';
const cartToken = `cart_${Math.random().toString(36).substring(2, 15)}_${Date.now()}`;

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to prompt user
function prompt(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

// Helper function to make API requests
async function makeRequest(endpoint, method = 'GET', body = null) {
  const headers = {
    'Content-Type': 'application/json',
    'Cart-Token': cartToken
  };

  // If we have a nonce, add it to the headers
  if (global.nonce) {
    headers['X-WC-Store-API-Nonce'] = global.nonce;
  }

  const options = {
    method,
    headers,
    body: body ? JSON.stringify(body) : null
  };

  console.log(`Making ${method} request to ${endpoint}`);
  if (body) {
    console.log('Request body:', JSON.stringify(body, null, 2));
  }

  try {
    const response = await fetch(`${baseUrl}${endpoint}`, options);
    
    // Store nonce from response headers if available
    const responseNonce = response.headers.get('X-WC-Store-API-Nonce');
    if (responseNonce) {
      global.nonce = responseNonce;
      console.log('Received nonce:', global.nonce);
    }

    // Also check if nonce is in the response body for some WooCommerce configurations
    if (!responseNonce && response.status === 200) {
      try {
        const data = await response.json();
        
        // Check for nonce in extensions or headers
        if (data.extensions && data.extensions.store_api_nonce) {
          global.nonce = data.extensions.store_api_nonce;
          console.log('Received nonce from body extensions:', global.nonce);
        }
        
        return data;
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error(`Failed to parse response: ${parseError.message}`);
      }
    } else if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} - ${errorText}`);
    } else {
      return await response.json();
    }
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

// Test functions
async function fetchNonce() {
  console.log('\n=== Fetching Nonce ===');
  try {
    const response = await makeRequest('/wp-json/wc/store/v1/cart');
    console.log('Cart response:', JSON.stringify(response, null, 2));
    return true;
  } catch (error) {
    console.error('Failed to fetch nonce:', error);
    return false;
  }
}

async function clearCart() {
  console.log('\n=== Clearing Cart ===');
  try {
    const response = await makeRequest('/wp-json/wc/store/v1/cart/items', 'DELETE');
    console.log('Cart cleared:', JSON.stringify(response, null, 2));
    return true;
  } catch (error) {
    console.error('Failed to clear cart:', error);
    return false;
  }
}

async function addItemToCart(productId, quantity = 1, variationId = null) {
  console.log(`\n=== Adding Item to Cart (Product ID: ${productId}) ===`);
  
  const cartData = {
    id: Number(productId),
    quantity
  };

  if (variationId) {
    cartData.variation_id = Number(variationId);
  }

  try {
    const response = await makeRequest('/wp-json/wc/store/v1/cart/add-item', 'POST', cartData);
    console.log('Item added to cart:', JSON.stringify(response, null, 2));
    return true;
  } catch (error) {
    console.error('Failed to add item to cart:', error);
    return false;
  }
}

async function getCart() {
  console.log('\n=== Getting Cart Contents ===');
  try {
    const response = await makeRequest('/wp-json/wc/store/v1/cart');
    console.log('Cart contents:', JSON.stringify(response, null, 2));
    return response;
  } catch (error) {
    console.error('Failed to get cart:', error);
    return null;
  }
}

async function processCheckout(billingData, shippingData) {
  console.log('\n=== Processing Checkout ===');
  
  const checkoutData = {
    billing_address: billingData,
    shipping_address: shippingData,
    payment_method: 'cod', // Cash on delivery for testing
    customer_note: 'Test order from Store API integration script'
  };

  try {
    const response = await makeRequest('/wp-json/wc/store/v1/checkout', 'POST', checkoutData);
    console.log('Checkout response:', JSON.stringify(response, null, 2));
    return response;
  } catch (error) {
    console.error('Failed to process checkout:', error);
    return null;
  }
}

// Main test flow
async function runTests() {
  console.log('=== WooCommerce Store API Integration Test ===');
  console.log(`Base URL: ${baseUrl}`);
  console.log(`Cart Token: ${cartToken}`);

  // Step 1: Fetch nonce
  if (!await fetchNonce()) {
    console.error('Failed to fetch nonce. Aborting tests.');
    return;
  }

  // Step 2: Clear cart
  if (!await clearCart()) {
    console.error('Failed to clear cart. Continuing with tests...');
  }

  // Step 3: Get product ID from user
  const productId = await prompt('Enter a product ID to add to cart: ');
  
  // Step 4: Add item to cart
  if (!await addItemToCart(productId, 1)) {
    console.error('Failed to add item to cart. Aborting tests.');
    return;
  }

  // Step 5: Get cart contents
  const cart = await getCart();
  if (!cart) {
    console.error('Failed to get cart contents. Aborting tests.');
    return;
  }

  // Step 6: Ask if user wants to test checkout
  const testCheckout = await prompt('Do you want to test checkout? (yes/no): ');
  
  if (testCheckout.toLowerCase() === 'yes') {
    // Step 7: Process checkout with test data
    const billingData = {
      first_name: 'Test',
      last_name: 'User',
      address_1: '123 Test St',
      city: 'Test City',
      state: 'TS',
      postcode: '12345',
      country: 'US',
      email: '<EMAIL>',
      phone: '555-1234'
    };
    
    const shippingData = { ...billingData };
    
    const checkoutResult = await processCheckout(billingData, shippingData);
    
    if (checkoutResult) {
      console.log('\n=== Checkout Successful ===');
      console.log(`Order ID: ${checkoutResult.order_id}`);
      console.log(`Order Status: ${checkoutResult.status}`);
      
      if (checkoutResult.payment_result?.redirect_url) {
        console.log(`Redirect URL: ${checkoutResult.payment_result.redirect_url}`);
      }
    }
  }

  console.log('\n=== Tests Completed ===');
  rl.close();
}

// Run the tests
runTests().catch(error => {
  console.error('Test script error:', error);
  rl.close();
}); 
import { NextRequest, NextResponse } from 'next/server';
import { GraphQLClient, gql } from 'graphql-request';
import crypto from 'crypto';

// Initialize GraphQL client for WooCommerce
const endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || 'https://your-wordpress-site.com/graphql';
const graphQLClient = new GraphQLClient(endpoint);

// Razorpay API credentials
const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;

// Update order mutation
const UPDATE_ORDER_MUTATION = gql`
  mutation UpdateOrder($input: UpdateOrderInput!) {
    updateOrder(input: $input) {
      clientMutationId
      order {
        id
        databaseId
        status
      }
    }
  }
`;

// Get order by meta query
const GET_ORDER_BY_RAZORPAY_ID = gql`
  query GetOrderByRazorpayId($metaKey: String!, $metaValue: String!) {
    orders(where: {
      metaQuery: {
        metaArray: [
          {
            key: $metaKey,
            value: $metaValue,
            compareOperator: EQUAL_TO
          }
        ]
      },
      orderby: [{ field: DATE, order: DESC }],
      first: 1
    }) {
      nodes {
        id
        databaseId
        orderNumber
        status
        total
      }
    }
  }
`;

export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json();
    const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = body;
    
    // Validate required fields
    if (!razorpay_payment_id || !razorpay_order_id || !razorpay_signature) {
      return NextResponse.json(
        { success: false, message: 'Missing required payment data' },
        { status: 400 }
      );
    }
    
    if (!RAZORPAY_KEY_SECRET) {
      return NextResponse.json(
        { success: false, message: 'Razorpay API credentials not configured' },
        { status: 500 }
      );
    }
    
    // Verify signature
    const payload = `${razorpay_order_id}|${razorpay_payment_id}`;
    const expectedSignature = crypto
      .createHmac('sha256', RAZORPAY_KEY_SECRET)
      .update(payload)
      .digest('hex');
    
    const isSignatureValid = expectedSignature === razorpay_signature;
    
    if (!isSignatureValid) {
      return NextResponse.json(
        { success: false, message: 'Invalid payment signature' },
        { status: 400 }
      );
    }
    
    // Find the WooCommerce order associated with this Razorpay order
    try {
      const orderResponse = await graphQLClient.request<{
        orders: {
          nodes: Array<{
            id: string;
            databaseId: number;
            orderNumber: string;
            status: string;
            total: string;
          }>;
        };
      }>(GET_ORDER_BY_RAZORPAY_ID, {
        metaKey: '_razorpay_order_id',
        metaValue: razorpay_order_id
      });
      
      const order = orderResponse.orders.nodes[0];
      
      if (!order) {
        return NextResponse.json(
          { 
            success: false, 
            verified: true, 
            message: 'Payment verified but no matching WooCommerce order found' 
          },
          { status: 404 }
        );
      }
      
      // Update the order status to processing (payment completed)
      const updateResponse = await graphQLClient.request<{
        updateOrder: {
          order: {
            id: string;
            databaseId: number;
            status: string;
          };
        };
      }>(UPDATE_ORDER_MUTATION, {
        input: {
          clientMutationId: 'update_order_after_payment',
          id: order.id,
          status: 'PROCESSING',
          metaData: [
            { key: '_razorpay_payment_id', value: razorpay_payment_id },
            { key: '_payment_transaction_id', value: razorpay_payment_id }
          ]
        }
      });
      
      return NextResponse.json({
        success: true,
        verified: true,
        orderId: order.databaseId,
        orderNumber: order.orderNumber
      });
      
    } catch (wooError) {
      console.error('Error updating WooCommerce order:', wooError);
      
      // Payment is still verified even if we couldn't update the order
      return NextResponse.json({
        success: true,
        verified: true,
        message: 'Payment verified but failed to update order status',
        error: wooError instanceof Error ? wooError.message : 'Unknown error'
      });
    }
    
  } catch (error) {
    console.error('Error verifying Razorpay payment:', error);
    
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
} 
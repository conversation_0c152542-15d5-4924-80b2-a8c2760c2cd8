import * as woocommerce from './woocommerce';
import { getInventoryMapping, updateInventoryMapping } from './wooInventoryMapping';
import { revalidatePath } from 'next/cache';

/**
 * Reconciliation utility for making sure WooCommerce inventory data is synchronized
 * This helps mitigate issues with missed webhooks and ensures data consistency
 */

// Store the last time we ran a reconciliation for each entity type
const lastReconciliationTimes: Record<string, number> = {
  'all-products': 0,
  'product-inventory': 0
};

// Cache duration in milliseconds
const CACHE_DURATION = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  LONG: 3 * 60 * 60 * 1000 // 3 hours
};

/**
 * Reconciles inventory data between WooCommerce and our local mapping
 * @param handle Optional product handle to only reconcile a specific product
 */
export async function reconcileInventory(handle?: string) {
  try {
    console.log(`Starting inventory reconciliation${handle ? ` for product ${handle}` : ''}...`);
    
    // Get products from WooCommerce - either all or a specific one
    let products = [];
    if (handle) {
      const product = await woocommerce.getProductBySlug(handle);
      if (product) {
        products = [product];
      }
    } else {
      const productResponse = await woocommerce.getProducts();
      products = productResponse.nodes || [];
    }
    
    if (products.length === 0) {
      console.log('No products found to reconcile inventory');
      return {
        success: true,
        stats: { updated: 0, added: 0, unchanged: 0 }
      };
    }
    
    // Get current inventory mapping
    const inventoryMapping = await getInventoryMapping();
    
    // Track stats
    let updated = 0;
    let unchanged = 0;
    let added = 0;
    
    // Process each product
    for (const product of products) {
      const productId = product.id.toString();
      
      // Check if product exists in mapping
      if (inventoryMapping[productId]) {
        // Update inventory if changed
        if (inventoryMapping[productId].inventory !== product.stock_quantity) {
          inventoryMapping[productId].inventory = product.stock_quantity || 0;
          updated++;
        } else {
          unchanged++;
        }
      } else {
        // Add new product to mapping
        inventoryMapping[productId] = {
          wooId: productId,
          inventory: product.stock_quantity || 0,
          sku: product.sku || '',
          title: product.name || '',
          lastUpdated: new Date().toISOString()
        };
        added++;
      }
    }
    
    // Save updated mapping
    await updateInventoryMapping(inventoryMapping);
    
    // Revalidate product pages
    if (handle) {
      revalidatePath(`/product/${handle}`);
    } else {
      revalidatePath('/product/[slug]');
      revalidatePath('/categories');
    }
    
    console.log(`Reconciliation complete: ${updated} updated, ${added} added, ${unchanged} unchanged`);
    
    return {
      success: true,
      stats: { updated, added, unchanged }
    };
  } catch (error) {
    console.error('Error during inventory reconciliation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Reconciles all products with WooCommerce
 * This is a heavy operation that should be used sparingly
 */
export async function reconcileAllProducts() {
  try {
    console.log('Starting full product reconciliation...');
    
    // Get all products from WooCommerce
    const productResponse = await woocommerce.getAllProducts(100);
    
    // Extract the products array
    let products: any[] = [];
    if (Array.isArray(productResponse)) {
      products = productResponse;
    } else if (productResponse && typeof productResponse === 'object') {
      // Handle case where it's returned as an object with nodes
      products = productResponse.nodes || [];
    }
    
    if (products.length === 0) {
      console.warn('No products found to reconcile');
      return {
        success: true,
        stats: { updated: 0, added: 0, unchanged: 0 }
      };
    }
    
    // Get current inventory mapping
    const inventoryMapping = await getInventoryMapping();
    
    // Track stats
    let updated = 0;
    let unchanged = 0;
    let added = 0;
    
    // Process each product
    for (const product of products) {
      const productId = product.id.toString();
      
      // Check if product exists in mapping
      if (inventoryMapping[productId]) {
        // Update product data if changed
        const currentMapping = inventoryMapping[productId];
        let hasChanges = false;
        
        // Check for inventory changes
        if (currentMapping.inventory !== product.stockQuantity) {
          currentMapping.inventory = product.stockQuantity || 0;
          hasChanges = true;
        }
        
        // Check for other metadata changes
        if (currentMapping.title !== product.name) {
          currentMapping.title = product.name;
          hasChanges = true;
        }
        
        if (currentMapping.sku !== product.sku) {
          currentMapping.sku = product.sku || '';
          hasChanges = true;
        }
        
        if (hasChanges) {
          currentMapping.lastUpdated = new Date().toISOString();
          updated++;
        } else {
          unchanged++;
        }
      } else {
        // Add new product to mapping
        inventoryMapping[productId] = {
          wooId: productId,
          inventory: product.stockQuantity || 0,
          sku: product.sku || '',
          title: product.name || '',
          lastUpdated: new Date().toISOString()
        };
        added++;
      }
    }
    
    // Save updated mapping
    await updateInventoryMapping(inventoryMapping);
    
    // Revalidate product and category pages
    revalidatePath('/product/[slug]');
    revalidatePath('/categories');
    revalidatePath('/category/[slug]');
    
    console.log(`Full reconciliation complete: ${updated} updated, ${added} added, ${unchanged} unchanged`);
    
    return {
      success: true,
      stats: { updated, added, unchanged }
    };
  } catch (error) {
    console.error('Error during full product reconciliation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Reconciles a specific product by its handle/slug
 * @param handle The product handle/slug
 * @returns The reconciliation result
 */
export async function reconcileProductByHandle(handle: string) {
  try {
    console.log(`Reconciling product with handle: ${handle}`);
    
    // Get the product from WooCommerce
    const product = await woocommerce.getProductBySlug(handle);
    
    if (!product) {
      return {
        success: false,
        error: `Product with handle ${handle} not found`
      };
    }
    
    // Get current inventory mapping
    const inventoryMapping = await getInventoryMapping();
    
    const productId = product.id.toString();
    let result = {
      action: '',
      product: handle
    };
    
    // Check if product exists in mapping
    if (inventoryMapping[productId]) {
      // Update product data if changed
      const currentMapping = inventoryMapping[productId];
      let hasChanges = false;
      
      // Check for inventory changes
      if (currentMapping.inventory !== product.stockQuantity) {
        currentMapping.inventory = product.stockQuantity || 0;
        hasChanges = true;
      }
      
      // Check for other metadata changes
      if (currentMapping.title !== product.name) {
        currentMapping.title = product.name;
        hasChanges = true;
      }
      
      if (currentMapping.sku !== product.sku) {
        currentMapping.sku = product.sku || '';
        hasChanges = true;
      }
      
      if (hasChanges) {
        currentMapping.lastUpdated = new Date().toISOString();
        result.action = 'updated';
      } else {
        result.action = 'unchanged';
      }
    } else {
      // Add new product to mapping
      inventoryMapping[productId] = {
        wooId: productId,
        inventory: product.stockQuantity || 0,
        sku: product.sku || '',
        title: product.name || '',
        lastUpdated: new Date().toISOString()
      };
      result.action = 'added';
    }
    
    // Save updated mapping
    await updateInventoryMapping(inventoryMapping);
    
    // Revalidate specific product page
    revalidatePath(`/product/${handle}`);
    
    console.log(`Product reconciliation complete for ${handle}: ${result.action}`);
    
    return {
      success: true,
      result
    };
  } catch (error) {
    console.error(`Error reconciling product ${handle}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Export the cache durations for reference elsewhere
export const ReconciliationCacheDuration = CACHE_DURATION; 
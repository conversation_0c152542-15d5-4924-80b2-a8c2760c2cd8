'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useCheckoutStore, CheckoutStep, Address } from '@/lib/checkoutStore';

const countries = [
  { code: 'IN', name: 'India' },
  { code: 'US', name: 'United States' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
];

export default function BillingForm() {
  const { 
    shippingAddress, 
    billingAddress, 
    sameAsShipping,
    setSameAsShipping,
    setBillingAddress, 
    setCurrentStep 
  } = useCheckoutStore();
  
  const { register, handleSubmit, reset, formState: { errors } } = useForm<Address & { email: string; phone: string }>({
    defaultValues: billingAddress || {
      ...shippingAddress,
      country: shippingAddress?.country || 'IN',
      email: '',
      phone: ''
    }
  });
  
  // Update form values when sameAsShipping changes
  useEffect(() => {
    if (sameAsShipping && shippingAddress) {
      reset({
        ...shippingAddress,
        email: billingAddress?.email || '',
        phone: billingAddress?.phone || ''
      });
    } else if (!sameAsShipping && billingAddress) {
      reset(billingAddress);
    }
  }, [sameAsShipping, shippingAddress, billingAddress, reset]);
  
  const onSubmit = (data: Address & { email: string; phone: string }) => {
    // If same as shipping, use shipping address but with email and phone
    const billingData = sameAsShipping && shippingAddress 
      ? { 
          ...shippingAddress, 
          email: data.email, 
          phone: data.phone 
        }
      : data;
    
    setBillingAddress(billingData);
    setCurrentStep(CheckoutStep.PAYMENT);
  };
  
  const handleBack = () => {
    setCurrentStep(CheckoutStep.SHIPPING_INFO);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">Billing Information</h2>
        <p className="text-gray-500 mt-1">Please enter your billing details</p>
      </div>
      
      <div className="flex items-center mb-4">
        <Checkbox
          id="sameAsShipping"
          checked={sameAsShipping}
          onCheckedChange={(checked) => setSameAsShipping(checked === true)}
        />
        <label htmlFor="sameAsShipping" className="ml-2 text-sm">
          Same as shipping address
        </label>
      </div>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              {...register('email', { 
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
              className={errors.email ? 'border-red-300' : ''}
            />
            {errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              {...register('phone', { required: 'Phone number is required' })}
              className={errors.phone ? 'border-red-300' : ''}
            />
            {errors.phone && (
              <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
            )}
          </div>
          
          {!sameAsShipping && (
            <>
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  {...register('firstName', { required: 'First name is required' })}
                  className={errors.firstName ? 'border-red-300' : ''}
                />
                {errors.firstName && (
                  <p className="text-sm text-red-500 mt-1">{errors.firstName.message}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  {...register('lastName', { required: 'Last name is required' })}
                  className={errors.lastName ? 'border-red-300' : ''}
                />
                {errors.lastName && (
                  <p className="text-sm text-red-500 mt-1">{errors.lastName.message}</p>
                )}
              </div>
              
              <div className="md:col-span-2">
                <Label htmlFor="address1">Address Line 1</Label>
                <Input
                  id="address1"
                  {...register('address1', { required: 'Address is required' })}
                  className={errors.address1 ? 'border-red-300' : ''}
                />
                {errors.address1 && (
                  <p className="text-sm text-red-500 mt-1">{errors.address1.message}</p>
                )}
              </div>
              
              <div className="md:col-span-2">
                <Label htmlFor="address2">Address Line 2 (Optional)</Label>
                <Input id="address2" {...register('address2')} />
              </div>
              
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  {...register('city', { required: 'City is required' })}
                  className={errors.city ? 'border-red-300' : ''}
                />
                {errors.city && (
                  <p className="text-sm text-red-500 mt-1">{errors.city.message}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  {...register('state', { required: 'State is required' })}
                  className={errors.state ? 'border-red-300' : ''}
                />
                {errors.state && (
                  <p className="text-sm text-red-500 mt-1">{errors.state.message}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="postcode">ZIP/Postal Code</Label>
                <Input
                  id="postcode"
                  {...register('postcode', { required: 'Postal code is required' })}
                  className={errors.postcode ? 'border-red-300' : ''}
                />
                {errors.postcode && (
                  <p className="text-sm text-red-500 mt-1">{errors.postcode.message}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="country">Country</Label>
                <select
                  id="country"
                  {...register('country', { required: 'Country is required' })}
                  className="w-full border border-gray-300 rounded-md p-2"
                >
                  {countries.map(country => (
                    <option key={country.code} value={country.code}>
                      {country.name}
                    </option>
                  ))}
                </select>
                {errors.country && (
                  <p className="text-sm text-red-500 mt-1">{errors.country.message}</p>
                )}
              </div>
            </>
          )}
        </div>
        
        <div className="flex justify-between pt-4">
          <Button 
            type="button" 
            variant="outline"
            onClick={handleBack}
          >
            Back to Shipping
          </Button>
          
          <Button 
            type="submit" 
            className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white"
          >
            Continue to Payment
          </Button>
        </div>
      </form>
    </div>
  );
} 
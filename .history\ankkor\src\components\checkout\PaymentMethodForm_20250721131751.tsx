'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useCheckoutStore, CheckoutStep, PaymentMethod } from '@/lib/checkoutStore';
import { CreditCard, Banknote } from 'lucide-react';

export default function PaymentMethodForm() {
  const { 
    paymentMethod,
    setPaymentMethod,
    setCurrentStep,
    customerNote,
    setCustomerNote
  } = useCheckoutStore();
  
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>(paymentMethod);
  
  const handleContinue = () => {
    setPaymentMethod(selectedMethod);
    setCurrentStep(CheckoutStep.REVIEW);
  };
  
  const handleBack = () => {
    setCurrentStep(CheckoutStep.BILLING_INFO);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">Payment Method</h2>
        <p className="text-gray-500 mt-1">Select your preferred payment method</p>
      </div>
      
      <RadioGroup 
        value={selectedMethod}
        onValueChange={(value) => setSelectedMethod(value as PaymentMethod)}
        className="space-y-4"
      >
        <div className={`flex items-center space-x-3 border p-4 rounded-md ${selectedMethod === PaymentMethod.RAZORPAY ? 'border-black bg-gray-50' : 'border-gray-200'}`}>
          <RadioGroupItem value={PaymentMethod.RAZORPAY} id="razorpay" />
          <Label htmlFor="razorpay" className="flex items-center gap-2 cursor-pointer">
            <CreditCard className="h-5 w-5" />
            <div>
              <p className="font-medium">Credit/Debit Card, UPI, Netbanking (Razorpay)</p>
              <p className="text-sm text-gray-500">Secure payment via Razorpay</p>
            </div>
          </Label>
        </div>
        
        <div className={`flex items-center space-x-3 border p-4 rounded-md ${selectedMethod === PaymentMethod.COD ? 'border-black bg-gray-50' : 'border-gray-200'}`}>
          <RadioGroupItem value={PaymentMethod.COD} id="cod" />
          <Label htmlFor="cod" className="flex items-center gap-2 cursor-pointer">
            <Banknote className="h-5 w-5" />
            <div>
              <p className="font-medium">Cash on Delivery</p>
              <p className="text-sm text-gray-500">Pay with cash when your order is delivered</p>
            </div>
          </Label>
        </div>
      </RadioGroup>
      
      <div className="space-y-2">
        <Label htmlFor="customerNote">Order Notes (Optional)</Label>
        <textarea
          id="customerNote"
          value={customerNote}
          onChange={(e) => setCustomerNote(e.target.value)}
          className="w-full border border-gray-300 rounded-md p-2 min-h-[100px]"
          placeholder="Special instructions for delivery or any other notes"
        />
      </div>
      
      <div className="flex justify-between pt-4">
        <Button 
          type="button" 
          variant="outline"
          onClick={handleBack}
        >
          Back to Billing
        </Button>
        
        <Button 
          onClick={handleContinue}
          className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white"
        >
          Review Order
        </Button>
      </div>
    </div>
  );
} 
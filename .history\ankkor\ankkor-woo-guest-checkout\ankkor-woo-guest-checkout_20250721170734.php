<?php
/**
 * Plugin Name: <PERSON><PERSON><PERSON> WooCommerce Guest Checkout
 * Description: Fixes guest checkout redirection issues for headless WooCommerce
 * Version: 1.0.0
 * Author: Ankkor
 * Text Domain: ankkor-woo-guest-checkout
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('ANKKOR_WOO_GC_PATH', plugin_dir_path(__FILE__));
define('ANKKOR_WOO_GC_URL', plugin_dir_url(__FILE__));
define('ANKKOR_WOO_GC_VERSION', '1.0.0');

// Include required files
require_once ANKKOR_WOO_GC_PATH . 'includes/class-ankkor-woo-guest-checkout.php';
require_once ANKKOR_WOO_GC_PATH . 'includes/class-ankkor-woo-api-handler.php';

// Initialize the plugin
function ankkor_woo_guest_checkout_init() {
    // Initialize main plugin class
    $ankkor_woo_gc = new Ankkor_Woo_Guest_Checkout();
    $ankkor_woo_gc->init();
    
    // Initialize API handler
    $ankkor_woo_api = new Ankkor_Woo_API_Handler();
    $ankkor_woo_api->init();
}
add_action('plugins_loaded', 'ankkor_woo_guest_checkout_init');

/**
 * Activation hook
 */
function ankkor_woo_guest_checkout_activate() {
    // Ensure WooCommerce is active
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die('This plugin requires WooCommerce to be installed and activated.');
    }
    
    // Enable guest checkout in WooCommerce settings
    update_option('woocommerce_enable_guest_checkout', 'yes');
    update_option('woocommerce_enable_checkout_login_reminder', 'no');
    
    // Flush rewrite rules
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'ankkor_woo_guest_checkout_activate');

/**
 * Deactivation hook
 */
function ankkor_woo_guest_checkout_deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'ankkor_woo_guest_checkout_deactivate'); 
<?php
/**
 * Main class for Ankkor WooCommerce Guest Checkout
 */
class Ankkor_Woo_Guest_Checkout {
    /**
     * Initialize the class and set up hooks
     */
    public function init() {
        // Disable admin access prevention for checkout and API requests
        add_filter('woocommerce_prevent_admin_access', array($this, 'disable_admin_access_prevention'), 10, 1);
        
        // Modify checkout URL to ensure it doesn't redirect to admin
        add_filter('woocommerce_get_checkout_url', array($this, 'modify_checkout_url'), 10, 1);
        
        // Disable checkout login form and registration requirements
        add_action('init', array($this, 'disable_checkout_login_requirements'), 5);
        
        // Prevent admin redirects for checkout pages
        add_action('template_redirect', array($this, 'prevent_admin_redirects'), 5);
        
        // Add query parameters to checkout URL
        add_filter('woocommerce_get_checkout_payment_url', array($this, 'add_guest_params_to_payment_url'), 10, 1);
        
        // Force guest checkout on all checkout pages
        add_action('woocommerce_before_checkout_form', array($this, 'force_guest_checkout'), 5);
        
        // Modify account endpoints to prevent login redirects
        add_filter('woocommerce_get_endpoint_url', array($this, 'modify_endpoint_url'), 10, 4);
        
        // Add custom checkout fields
        add_filter('woocommerce_checkout_fields', array($this, 'customize_checkout_fields'), 10, 1);
        
        // Add admin notice if guest checkout is disabled
        add_action('admin_notices', array($this, 'guest_checkout_admin_notice'));
    }
    
    /**
     * Disable admin access prevention for checkout and API requests
     */
    public function disable_admin_access_prevention($prevent_access) {
        // Check if this is a REST API or GraphQL request
        if (
            (defined('REST_REQUEST') && REST_REQUEST) ||
            (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/graphql') !== false) ||
            (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/checkout') !== false) ||
            (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/cart') !== false) ||
            (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes')
        ) {
            return false; // Don't prevent access for these requests
        }
        
        return $prevent_access;
    }
    
    /**
     * Modify checkout URL to add guest checkout parameters
     */
    public function modify_checkout_url($url) {
        // Add parameters to ensure guest checkout
        return add_query_arg(array(
            'guest_checkout' => 'yes',
            'checkout_woocommerce_checkout_login_reminder' => '0',
            'create_account' => '0',
            'skip_login' => '1',
            'force_guest_checkout' => '1'
        ), $url);
    }
    
    /**
     * Disable checkout login requirements
     */
    public function disable_checkout_login_requirements() {
        // Force guest checkout
        add_filter('woocommerce_checkout_registration_required', '__return_false');
        add_filter('woocommerce_checkout_registration_enabled', '__return_false');
        add_filter('woocommerce_checkout_is_registration_required', '__return_false');
        
        // Skip login for existing customers
        add_filter('woocommerce_checkout_must_be_logged_in', '__return_false');
        
        // Disable customer account creation during checkout
        add_filter('pre_option_woocommerce_enable_signup_and_login_from_checkout', function($value) {
            return 'no';
        });
        
        // Force guest checkout mode
        add_filter('pre_option_woocommerce_enable_guest_checkout', function($value) {
            return 'yes';
        });
    }
    
    /**
     * Prevent admin redirects for checkout pages
     */
    public function prevent_admin_redirects() {
        if (is_checkout() || isset($_GET['guest_checkout'])) {
            // Remove admin access prevention for checkout
            remove_action('template_redirect', 'wc_prevent_admin_access');
        }
    }
    
    /**
     * Add guest checkout parameters to payment URL
     */
    public function add_guest_params_to_payment_url($url) {
        return add_query_arg(array(
            'guest_checkout' => 'yes',
            'checkout_woocommerce_checkout_login_reminder' => '0',
            'create_account' => '0',
            'skip_login' => '1',
            'force_guest_checkout' => '1'
        ), $url);
    }
    
    /**
     * Force guest checkout on all checkout pages
     */
    public function force_guest_checkout() {
        if (!is_user_logged_in()) {
            // Add hidden fields to force guest checkout
            echo '<input type="hidden" name="guest_checkout" value="yes">';
            echo '<input type="hidden" name="checkout_woocommerce_checkout_login_reminder" value="0">';
            echo '<input type="hidden" name="create_account" value="0">';
            echo '<input type="hidden" name="skip_login" value="1">';
            echo '<input type="hidden" name="force_guest_checkout" value="1">';
        }
    }
    
    /**
     * Modify account endpoints to prevent login redirects
     */
    public function modify_endpoint_url($url, $endpoint, $value, $permalink) {
        if ($endpoint === 'checkout') {
            return add_query_arg(array(
                'guest_checkout' => 'yes',
                'checkout_woocommerce_checkout_login_reminder' => '0',
                'create_account' => '0',
                'skip_login' => '1',
                'force_guest_checkout' => '1'
            ), $url);
        }
        
        return $url;
    }
    
    /**
     * Customize checkout fields
     */
    public function customize_checkout_fields($fields) {
        // Remove account fields if guest checkout
        if (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes') {
            unset($fields['account']);
        }
        
        return $fields;
    }
    
    /**
     * Admin notice if guest checkout is disabled
     */
    public function guest_checkout_admin_notice() {
        // Check if current user can manage options
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Check if guest checkout is disabled
        if (get_option('woocommerce_enable_guest_checkout') !== 'yes') {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p><strong>Ankkor WooCommerce Guest Checkout:</strong> Guest checkout is currently disabled in your WooCommerce settings. This plugin will force-enable it via URL parameters, but we recommend enabling it in <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=account'); ?>">WooCommerce Settings &gt; Accounts &amp; Privacy</a> for the best experience.</p>
            </div>
            <?php
        }
    }
} 
'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useLocalCartStore, CartItem } from './localCartStore';
import { createRazorpayOrder, verifyRazorpayPayment, RazorpaySuccessResponse } from './razorpay';

// Checkout steps
export enum CheckoutStep {
  SHIPPING_INFO = 'shipping_info',
  BILLING_INFO = 'billing_info',
  PAYMENT = 'payment',
  REVIEW = 'review',
  CONFIRMATION = 'confirmation',
}

// Address interface
export interface Address {
  firstName: string;
  lastName: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
  email?: string;
  phone?: string;
}

// Payment method types
export enum PaymentMethod {
  RAZORPAY = 'razorpay',
  COD = 'cod',
}

// Order interface
export interface Order {
  id?: number;
  orderNumber?: string;
  status?: string;
  total?: string;
  items: CartItem[];
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: PaymentMethod;
  customerNote?: string;
  razorpayOrderId?: string;
  razorpayPaymentId?: string;
}

// Checkout state interface
interface CheckoutState {
  currentStep: CheckoutStep;
  shippingAddress: Address | null;
  billingAddress: Address | null;
  sameAsShipping: boolean;
  paymentMethod: PaymentMethod;
  customerNote: string;
  order: Order | null;
  isLoading: boolean;
  error: string | null;
  razorpayOrderId: string | null;
  
  // Actions
  setCurrentStep: (step: CheckoutStep) => void;
  setShippingAddress: (address: Address) => void;
  setBillingAddress: (address: Address) => void;
  setSameAsShipping: (same: boolean) => void;
  setPaymentMethod: (method: PaymentMethod) => void;
  setCustomerNote: (note: string) => void;
  setOrder: (order: Order | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setRazorpayOrderId: (orderId: string | null) => void;
  
  // Async actions
  createOrder: () => Promise<Order | null>;
  processPayment: (paymentData?: any) => Promise<boolean>;
  resetCheckout: () => void;
}

// Create the checkout store
export const useCheckoutStore = create<CheckoutState>()(
  persist(
    (set, get) => ({
      currentStep: CheckoutStep.SHIPPING_INFO,
      shippingAddress: null,
      billingAddress: null,
      sameAsShipping: true,
      paymentMethod: PaymentMethod.RAZORPAY,
      customerNote: '',
      order: null,
      isLoading: false,
      error: null,
      razorpayOrderId: null,
      
      // Actions
      setCurrentStep: (step) => set({ currentStep: step }),
      setShippingAddress: (address) => set({ shippingAddress: address }),
      setBillingAddress: (address) => set({ billingAddress: address }),
      setSameAsShipping: (same) => set({ sameAsShipping: same }),
      setPaymentMethod: (method) => set({ paymentMethod: method }),
      setCustomerNote: (note) => set({ customerNote: note }),
      setOrder: (order) => set({ order }),
      setIsLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      setRazorpayOrderId: (orderId) => set({ razorpayOrderId: orderId }),
      
      // Async actions
      createOrder: async () => {
        const { 
          shippingAddress, 
          billingAddress, 
          sameAsShipping, 
          paymentMethod,
          customerNote,
          setIsLoading,
          setError,
          setOrder,
          setRazorpayOrderId
        } = get();
        
        const cartStore = useLocalCartStore.getState();
        const items = cartStore.items;
        const total = cartStore.total();
        
        if (items.length === 0) {
          setError('Your cart is empty');
          return null;
        }
        
        if (!shippingAddress) {
          setError('Shipping address is required');
          return null;
        }
        
        const finalBillingAddress = sameAsShipping ? shippingAddress : billingAddress;
        
        if (!finalBillingAddress) {
          setError('Billing address is required');
          return null;
        }
        
        setIsLoading(true);
        setError(null);
        
        try {
          // Prepare order data
          const orderData = {
            billing: {
              first_name: finalBillingAddress.firstName,
              last_name: finalBillingAddress.lastName,
              email: finalBillingAddress.email,
              phone: finalBillingAddress.phone,
              address_1: finalBillingAddress.address1,
              address_2: finalBillingAddress.address2 || '',
              city: finalBillingAddress.city,
              state: finalBillingAddress.state,
              postcode: finalBillingAddress.postcode,
              country: finalBillingAddress.country
            },
            shipping: {
              first_name: shippingAddress.firstName,
              last_name: shippingAddress.lastName,
              address_1: shippingAddress.address1,
              address_2: shippingAddress.address2 || '',
              city: shippingAddress.city,
              state: shippingAddress.state,
              postcode: shippingAddress.postcode,
              country: shippingAddress.country
            },
            payment_method: paymentMethod,
            customer_note: customerNote,
            line_items: items.map(item => ({
              product_id: parseInt(item.productId),
              variation_id: item.variationId ? parseInt(item.variationId) : undefined,
              quantity: item.quantity
            }))
          };
          
          // For Razorpay, create a payment order first
          if (paymentMethod === PaymentMethod.RAZORPAY) {
            const razorpayResponse = await createRazorpayOrder(
              total,
              `order_${Date.now()}`,
              { 
                customer_email: finalBillingAddress.email || '',
                customer_phone: finalBillingAddress.phone || ''
              }
            );
            
            if (razorpayResponse && razorpayResponse.id) {
              setRazorpayOrderId(razorpayResponse.id);
              
              // Create a new order with the Razorpay order ID
              const order: Order = {
                items,
                shippingAddress,
                billingAddress: finalBillingAddress,
                paymentMethod,
                customerNote,
                razorpayOrderId: razorpayResponse.id,
                total: total.toString()
              };
              
              setOrder(order);
              setIsLoading(false);
              return order;
            } else {
              throw new Error('Failed to create Razorpay order');
            }
          } else {
            // For COD, create a WooCommerce order directly
            const response = await fetch('/api/checkout', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(orderData)
            });
            
            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.message || 'Failed to create order');
            }
            
            const result = await response.json();
            
            const order: Order = {
              id: result.orderId,
              orderNumber: result.orderNumber,
              status: result.status,
              items,
              shippingAddress,
              billingAddress: finalBillingAddress,
              paymentMethod,
              customerNote
            };
            
            setOrder(order);
            setIsLoading(false);
            return order;
          }
        } catch (error) {
          console.error('Error creating order:', error);
          setError(error instanceof Error ? error.message : 'Failed to create order');
          setIsLoading(false);
          return null;
        }
      },
      
      processPayment: async (paymentData?: RazorpaySuccessResponse) => {
        const { 
          order, 
          razorpayOrderId,
          setIsLoading,
          setError
        } = get();
        
        if (!order) {
          setError('No order found');
          return false;
        }
        
        setIsLoading(true);
        setError(null);
        
        try {
          if (order.paymentMethod === PaymentMethod.RAZORPAY) {
            if (!paymentData) {
              setError('Payment data is required for Razorpay');
              setIsLoading(false);
              return false;
            }
            
            // Verify the Razorpay payment
            const verificationResult = await verifyRazorpayPayment(paymentData);
            
            if (!verificationResult.verified) {
              throw new Error('Payment verification failed');
            }
            
            // Update the order with payment details
            set({
              order: {
                ...order,
                id: verificationResult.orderId,
                razorpayPaymentId: paymentData.razorpay_payment_id
              }
            });
            
            // Clear the cart after successful payment
            useLocalCartStore.getState().clearCart();
            
            setIsLoading(false);
            return true;
          } else if (order.paymentMethod === PaymentMethod.COD) {
            // For COD, the order is already created
            // Clear the cart after successful order placement
            useLocalCartStore.getState().clearCart();
            
            setIsLoading(false);
            return true;
          }
          
          setIsLoading(false);
          return false;
        } catch (error) {
          console.error('Error processing payment:', error);
          setError(error instanceof Error ? error.message : 'Payment processing failed');
          setIsLoading(false);
          return false;
        }
      },
      
      resetCheckout: () => {
        set({
          currentStep: CheckoutStep.SHIPPING_INFO,
          order: null,
          error: null,
          razorpayOrderId: null
        });
      }
    }),
    {
      name: 'ankkor-checkout',
      // Only persist certain fields
      partialize: (state) => ({
        currentStep: state.currentStep,
        shippingAddress: state.shippingAddress,
        billingAddress: state.billingAddress,
        sameAsShipping: state.sameAsShipping,
        paymentMethod: state.paymentMethod,
        customerNote: state.customerNote,
        order: state.order,
        razorpayOrderId: state.razorpayOrderId
      }),
    }
  )
);

// Helper hooks
export const useCheckoutStep = () => useCheckoutStore(state => state.currentStep);
export const useShippingAddress = () => useCheckoutStore(state => state.shippingAddress);
export const useBillingAddress = () => useCheckoutStore(state => state.billingAddress);
export const useSameAsShipping = () => useCheckoutStore(state => state.sameAsShipping);
export const usePaymentMethod = () => useCheckoutStore(state => state.paymentMethod);
export const useOrder = () => useCheckoutStore(state => state.order);
export const useCheckoutLoading = () => useCheckoutStore(state => state.isLoading);
export const useCheckoutError = () => useCheckoutStore(state => state.error); 
'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useCheckoutStore, CheckoutStep, PaymentMethod } from '@/lib/checkoutStore';
import { useLocalCartStore } from '@/lib/localCartStore';
import { loadRazorpay, initializeRazorpayCheckout } from '@/lib/razorpay';
import { Loader2, CreditCard, Banknote, MapPin, Phone, Mail, FileText } from 'lucide-react';

export default function ReviewOrder() {
  const router = useRouter();
  const { 
    shippingAddress, 
    billingAddress,
    paymentMethod,
    customerNote,
    order,
    razorpayOrderId,
    isLoading,
    error,
    setError,
    setCurrentStep,
    createOrder,
    processPayment,
    setIsLoading
  } = useCheckoutStore();
  
  const cartStore = useLocalCartStore();
  const [processingPayment, setProcessingPayment] = useState(false);
  
  const handleBack = () => {
    setCurrentStep(CheckoutStep.PAYMENT);
  };
  
  const handlePlaceOrder = async () => {
    try {
      setProcessingPayment(true);
      
      // Create or get the order
      const orderData = order || await createOrder();
      if (!orderData) {
        throw new Error('Failed to create order');
      }
      
      if (paymentMethod === PaymentMethod.RAZORPAY) {
        // For Razorpay, we need to initialize the payment
        if (!razorpayOrderId) {
          throw new Error('Razorpay order ID not found');
        }
        
        // Load Razorpay SDK
        const isLoaded = await loadRazorpay();
        if (!isLoaded) {
          throw new Error('Failed to load Razorpay SDK');
        }
        
        // Initialize Razorpay checkout
        const options = {
          key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || '',
          amount: parseInt(orderData.total || '0') * 100, // in paise
          currency: 'INR',
          name: 'Ankkor',
          description: 'Purchase from Ankkor',
          image: '/logo.PNG',
          order_id: razorpayOrderId,
          prefill: {
            name: `${billingAddress?.firstName} ${billingAddress?.lastName}`,
            email: billingAddress?.email,
            contact: billingAddress?.phone
          },
          theme: {
            color: '#2c2c27'
          },
          handler: async (response: any) => {
            try {
              // Process the payment
              const success = await processPayment(response);
              
              if (success) {
                // Navigate to success page
                router.push('/checkout/success');
              } else {
                throw new Error('Payment processing failed');
              }
            } catch (error) {
              console.error('Payment processing error:', error);
              setError(error instanceof Error ? error.message : 'Payment processing failed');
            } finally {
              setProcessingPayment(false);
            }
          }
        };
        
        // Open Razorpay checkout
        await initializeRazorpayCheckout(options);
      } else {
        // For COD, just process the order
        const success = await processPayment();
        
        if (success) {
          // Navigate to success page
          router.push('/checkout/success');
        } else {
          throw new Error('Order processing failed');
        }
      }
    } catch (error) {
      console.error('Place order error:', error);
      setError(error instanceof Error ? error.message : 'Failed to place order');
      setProcessingPayment(false);
    }
  };
  
  // Format address for display
  const formatAddress = (address: any) => {
    if (!address) return '';
    
    const parts = [
      address.address1,
      address.address2,
      address.city,
      address.state,
      address.postcode,
      address.country
    ].filter(Boolean);
    
    return parts.join(', ');
  };
  
  // Calculate totals
  const subtotal = cartStore.subtotal();
  const shipping = 0; // Free shipping for now
  const tax = 0; // Tax calculation would go here
  const total = subtotal + shipping + tax;
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">Review Your Order</h2>
        <p className="text-gray-500 mt-1">Please review your order details before placing your order</p>
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 p-3 rounded-md">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}
      
      <div className="space-y-4">
        {/* Shipping Address */}
        <div className="border rounded-md p-4">
          <div className="flex items-center gap-2 mb-2">
            <MapPin className="h-4 w-4" />
            <h3 className="font-medium">Shipping Address</h3>
          </div>
          <p className="text-sm">
            {shippingAddress?.firstName} {shippingAddress?.lastName}<br />
            {formatAddress(shippingAddress)}
          </p>
        </div>
        
        {/* Billing Address */}
        <div className="border rounded-md p-4">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="h-4 w-4" />
            <h3 className="font-medium">Billing Address</h3>
          </div>
          <p className="text-sm">
            {billingAddress?.firstName} {billingAddress?.lastName}<br />
            {formatAddress(billingAddress)}
          </p>
          <div className="flex items-center gap-2 mt-2">
            <Mail className="h-4 w-4" />
            <span className="text-sm">{billingAddress?.email}</span>
          </div>
          <div className="flex items-center gap-2 mt-1">
            <Phone className="h-4 w-4" />
            <span className="text-sm">{billingAddress?.phone}</span>
          </div>
        </div>
        
        {/* Payment Method */}
        <div className="border rounded-md p-4">
          <div className="flex items-center gap-2 mb-2">
            {paymentMethod === PaymentMethod.RAZORPAY ? (
              <CreditCard className="h-4 w-4" />
            ) : (
              <Banknote className="h-4 w-4" />
            )}
            <h3 className="font-medium">Payment Method</h3>
          </div>
          <p className="text-sm">
            {paymentMethod === PaymentMethod.RAZORPAY 
              ? 'Credit/Debit Card, UPI, Netbanking (Razorpay)' 
              : 'Cash on Delivery'}
          </p>
        </div>
        
        {/* Order Items */}
        <div className="border rounded-md p-4">
          <h3 className="font-medium mb-3">Order Items</h3>
          <ul className="divide-y">
            {cartStore.items.map(item => (
              <li key={item.id} className="py-3 flex justify-between">
                <div>
                  <p className="font-medium">{item.name}</p>
                  <p className="text-sm text-gray-500">
                    Quantity: {item.quantity}
                    {item.attributes?.map(attr => 
                      ` • ${attr.name}: ${attr.value}`
                    )}
                  </p>
                </div>
                                 <p className="font-medium">₹{(typeof item.price === 'string' ? parseFloat(item.price) : item.price * item.quantity).toFixed(2)}</p>
              </li>
            ))}
          </ul>
          
          {/* Order Summary */}
          <div className="border-t mt-3 pt-3 space-y-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal</span>
              <span>₹{subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Shipping</span>
              <span>{shipping === 0 ? 'Free' : `₹${shipping.toFixed(2)}`}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Tax</span>
              <span>₹{tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-medium pt-2 border-t">
              <span>Total</span>
              <span>₹{total.toFixed(2)}</span>
            </div>
          </div>
        </div>
        
        {/* Order Notes */}
        {customerNote && (
          <div className="border rounded-md p-4">
            <h3 className="font-medium mb-2">Order Notes</h3>
            <p className="text-sm">{customerNote}</p>
          </div>
        )}
      </div>
      
      <div className="flex justify-between pt-4">
        <Button 
          type="button" 
          variant="outline"
          onClick={handleBack}
          disabled={processingPayment || isLoading}
        >
          Back to Payment
        </Button>
        
        <Button 
          onClick={handlePlaceOrder}
          disabled={processingPayment || isLoading}
          className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white"
        >
          {processingPayment || isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            `Place Order - ₹${total.toFixed(2)}`
          )}
        </Button>
      </div>
    </div>
  );
} 
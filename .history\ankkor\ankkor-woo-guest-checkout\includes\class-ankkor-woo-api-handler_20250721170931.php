<?php
/**
 * API Handler class for Ankkor WooCommerce Guest Checkout
 */
class Ankkor_Woo_API_Handler {
    /**
     * Initialize the class and set up hooks
     */
    public function init() {
        // Add CORS headers for API requests
        add_action('rest_api_init', array($this, 'add_cors_headers'));
        
        // Allow guest checkout for API requests
        add_filter('woocommerce_rest_check_permissions', array($this, 'allow_guest_checkout_api'), 10, 4);
        
        // Register custom REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        
        // Modify GraphQL response to include guest checkout data
        add_filter('graphql_response_headers_to_send', array($this, 'add_graphql_headers'));
        
        // Filter WooCommerce Store API responses
        add_filter('woocommerce_store_api_checkout_update_order_from_request', array($this, 'modify_store_api_order'), 10, 2);
        
        // Add custom headers to all API responses
        add_filter('rest_pre_serve_request', array($this, 'add_api_headers'), 10, 4);
    }
    
    /**
     * Add CORS headers for API requests
     */
    public function add_cors_headers() {
        // Get the origin
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
        
        // Allow requests from any origin for development
        // In production, you might want to restrict this to specific domains
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce, X-WC-Store-API-Nonce');
        
        // Handle preflight OPTIONS requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            status_header(200);
            exit;
        }
    }
    
    /**
     * Allow guest checkout for API requests
     */
    public function allow_guest_checkout_api($permission, $context, $object_id, $post_type) {
        // If this is a checkout-related request, allow it
        if (
            strpos($_SERVER['REQUEST_URI'], '/checkout') !== false ||
            strpos($_SERVER['REQUEST_URI'], '/cart') !== false ||
            strpos($_SERVER['REQUEST_URI'], '/store/v1') !== false ||
            isset($_GET['guest_checkout'])
        ) {
            return true;
        }
        
        return $permission;
    }
    
    /**
     * Register custom REST API endpoints
     */
    public function register_rest_routes() {
        register_rest_route('ankkor/v1', '/guest-checkout', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_guest_checkout_url'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('ankkor/v1', '/fix-checkout', array(
            'methods' => 'POST',
            'callback' => array($this, 'fix_checkout_session'),
            'permission_callback' => '__return_true',
        ));
    }
    
    /**
     * Get guest checkout URL
     */
    public function get_guest_checkout_url($request) {
        // Get the checkout URL with guest parameters
        $checkout_url = wc_get_checkout_url();
        $checkout_url = add_query_arg(array(
            'guest_checkout' => 'yes',
            'checkout_woocommerce_checkout_login_reminder' => '0',
            'create_account' => '0',
            'skip_login' => '1',
            'force_guest_checkout' => '1'
        ), $checkout_url);
        
        return rest_ensure_response(array(
            'success' => true,
            'checkout_url' => $checkout_url,
        ));
    }
    
    /**
     * Fix checkout session
     */
    public function fix_checkout_session($request) {
        // Get request parameters
        $params = $request->get_params();
        
        // Force guest checkout in session
        if (function_exists('WC') && WC()->session) {
            WC()->session->set('force_guest_checkout', true);
        }
        
        // Return success response
        return rest_ensure_response(array(
            'success' => true,
            'message' => 'Checkout session fixed for guest checkout',
        ));
    }
    
    /**
     * Add headers to GraphQL responses
     */
    public function add_graphql_headers($headers) {
        // Add custom headers for GraphQL responses
        $headers['X-Guest-Checkout-Enabled'] = 'true';
        
        return $headers;
    }
    
    /**
     * Modify Store API order
     */
    public function modify_store_api_order($order, $request) {
        // Set order as guest checkout
        $order->set_customer_id(0);
        $order->set_created_via('headless_guest_checkout');
        
        // Add order note
        $order->add_order_note('Order placed via headless guest checkout');
        
        return $order;
    }
    
    /**
     * Add custom headers to all API responses
     */
    public function add_api_headers($served, $result, $request, $server) {
        // Add custom headers to API responses
        header('X-Guest-Checkout-Enabled: true');
        
        return $served;
    }
} 
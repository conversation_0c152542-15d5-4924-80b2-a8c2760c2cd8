'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckCircle, Package, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCheckoutStore } from '@/lib/checkoutStore';

export default function CheckoutSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('order_id');
  
  const { order, resetCheckout } = useCheckoutStore();
  
  // Redirect to home if no order is found
  useEffect(() => {
    if (!order && !orderId) {
      router.push('/');
    }
    
    // Reset checkout state on component unmount
    return () => {
      resetCheckout();
    };
  }, [order, orderId, router, resetCheckout]);
  
  // Use the order from the store or fallback to the order ID from URL
  const displayOrderId = order?.id || orderId;
  const displayOrderNumber = order?.orderNumber || `#${displayOrderId}`;
  
  if (!order && !orderId) {
    return null; // Will redirect in useEffect
  }
  
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto text-center">
        <div className="flex justify-center mb-6">
          <CheckCircle className="h-16 w-16 text-green-500" />
        </div>
        
        <h1 className="text-3xl font-serif mb-4">Thank You for Your Order!</h1>
        
        <p className="text-gray-600 mb-8">
          Your order has been received and is now being processed. 
          We've sent a confirmation email with your order details.
        </p>
        
        <div className="bg-white p-6 border rounded-lg shadow-sm mb-8">
          <h2 className="text-xl font-medium mb-4">Order Details</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-left">
              <p className="text-gray-500">Order Number</p>
              <p className="font-medium">{displayOrderNumber}</p>
            </div>
            <div className="text-left">
              <p className="text-gray-500">Date</p>
              <p className="font-medium">{new Date().toLocaleDateString()}</p>
            </div>
            <div className="text-left">
              <p className="text-gray-500">Status</p>
              <p className="font-medium">{order?.status || 'Processing'}</p>
            </div>
            <div className="text-left">
              <p className="text-gray-500">Total</p>
              <p className="font-medium">₹{order?.total || '0.00'}</p>
            </div>
          </div>
          
          <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
            <Package className="h-5 w-5 text-gray-500 mr-2" />
            <p className="text-gray-600">
              You will receive shipping updates via email
            </p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/account">
            <Button variant="outline" className="flex items-center">
              View Order History
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link href="/">
            <Button className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white">
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 
/**
 * CartSession Utility for WooCommerce Store API
 * 
 * This module handles the WooCommerce cart token generation, storage, and retrieval.
 * It provides methods to get the necessary headers for Store API requests.
 */

'use client';

const CART_TOKEN_KEY = 'wc_cart_token';

/**
 * CartSession class to manage WooCommerce cart tokens
 */
export class CartSession {
  private static instance: CartSession;
  private cartToken: string | null = null;

  private constructor() {
    this.initialize();
  }

  /**
   * Get the singleton instance of CartSession
   */
  public static getInstance(): CartSession {
    if (!CartSession.instance) {
      CartSession.instance = new CartSession();
    }
    return CartSession.instance;
  }

  /**
   * Initialize the cart session by reading from localStorage or generating a new token
   */
  private initialize(): void {
    if (typeof window === 'undefined') return;

    try {
      // Try to get existing token from localStorage
      const storedToken = localStorage.getItem(CART_TOKEN_KEY);
      
      if (storedToken) {
        this.cartToken = storedToken;
      } else {
        // Generate a new token if none exists
        this.cartToken = this.generateCartToken();
        localStorage.setItem(CART_TOKEN_KEY, this.cartToken);
      }
    } catch (error) {
      console.error('Error initializing cart session:', error);
      // Generate a token in memory if localStorage fails
      this.cartToken = this.generateCartToken();
    }
  }

  /**
   * Generate a new random cart token
   */
  private generateCartToken(): string {
    return `cart_${Math.random().toString(36).substring(2, 15)}_${Date.now()}`;
  }

  /**
   * Get the current cart token
   */
  public getCartToken(): string {
    if (!this.cartToken) {
      this.initialize();
    }
    return this.cartToken || this.generateCartToken();
  }

  /**
   * Reset the cart token (e.g., after checkout completion)
   */
  public resetCartToken(): void {
    if (typeof window === 'undefined') return;
    
    try {
      this.cartToken = this.generateCartToken();
      localStorage.setItem(CART_TOKEN_KEY, this.cartToken);
    } catch (error) {
      console.error('Error resetting cart token:', error);
    }
  }

  /**
   * Get headers for Store API requests including Cart-Token and X-WC-Store-API-Nonce
   */
  public headers(nonce: string): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Cart-Token': this.getCartToken()
    };

    if (nonce) {
      headers['X-WC-Store-API-Nonce'] = nonce;
    }

    return headers;
  }
}

// Export a singleton instance
export const cartSession = CartSession.getInstance();

// Export a hook for React components
export function useCartSession() {
  return cartSession;
} 
'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { CheckCircle, Package, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface OrderDetails {
  id: number;
  orderNumber: string;
  status: string;
  total: string;
  date: string;
}

export default function CheckoutSuccessPage() {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('order_id');
  
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    async function fetchOrderDetails() {
      if (!orderId) {
        setError('No order ID provided');
        setLoading(false);
        return;
      }
      
      try {
        // In a real implementation, you would fetch order details from your API
        // For this example, we'll simulate a successful order
        
        // Simulated API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Simulated order data
        setOrderDetails({
          id: parseInt(orderId),
          orderNumber: `#${orderId}`,
          status: 'Processing',
          total: '$299.99',
          date: new Date().toLocaleDateString()
        });
        
        setError(null);
      } catch (err) {
        console.error('Error fetching order details:', err);
        setError('Failed to load order details');
      } finally {
        setLoading(false);
      }
    }
    
    fetchOrderDetails();
  }, [orderId]);
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="animate-pulse">
          <div className="h-8 w-64 bg-gray-200 rounded mx-auto mb-8"></div>
          <div className="h-4 w-full max-w-md bg-gray-200 rounded mx-auto mb-4"></div>
          <div className="h-4 w-full max-w-md bg-gray-200 rounded mx-auto mb-4"></div>
          <div className="h-12 w-48 bg-gray-200 rounded mx-auto mt-8"></div>
        </div>
      </div>
    );
  }
  
  if (error || !orderId) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-medium mb-4">Order Not Found</h1>
        <p className="text-gray-600 mb-8">
          {error || 'No order information was provided. Please check your order history or contact customer support.'}
        </p>
        <Link href="/">
          <Button className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white">
            Return to Home
          </Button>
        </Link>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto text-center">
        <div className="flex justify-center mb-6">
          <CheckCircle className="h-16 w-16 text-green-500" />
        </div>
        
        <h1 className="text-3xl font-serif mb-4">Thank You for Your Order!</h1>
        
        <p className="text-gray-600 mb-8">
          Your order has been received and is now being processed. 
          We've sent a confirmation email with your order details.
        </p>
        
        <div className="bg-white p-6 border rounded-lg shadow-sm mb-8">
          <h2 className="text-xl font-medium mb-4">Order Details</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-left">
              <p className="text-gray-500">Order Number</p>
              <p className="font-medium">{orderDetails?.orderNumber}</p>
            </div>
            <div className="text-left">
              <p className="text-gray-500">Date</p>
              <p className="font-medium">{orderDetails?.date}</p>
            </div>
            <div className="text-left">
              <p className="text-gray-500">Status</p>
              <p className="font-medium">{orderDetails?.status}</p>
            </div>
            <div className="text-left">
              <p className="text-gray-500">Total</p>
              <p className="font-medium">{orderDetails?.total}</p>
            </div>
          </div>
          
          <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
            <Package className="h-5 w-5 text-gray-500 mr-2" />
            <p className="text-gray-600">
              You will receive shipping updates via email
            </p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/account">
            <Button variant="outline" className="flex items-center">
              View Order History
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link href="/">
            <Button className="bg-[#2c2c27] hover:bg-[#3c3c37] text-white">
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 
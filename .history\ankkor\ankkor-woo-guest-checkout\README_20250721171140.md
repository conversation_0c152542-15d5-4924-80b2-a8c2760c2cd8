# Ankkor WooCommerce Guest Checkout

This plugin fixes guest checkout redirection issues for headless WooCommerce setups. It prevents users from being redirected to login pages and ensures they can proceed directly to checkout.

## The Problem

In headless WooCommerce setups, users are often redirected to the WordPress login page when trying to checkout, even when guest checkout is enabled. This happens because:

1. <PERSON>oo<PERSON>om<PERSON>ce tries to redirect admin-related URLs to the login page
2. The checkout process requires authentication for certain actions
3. Session cookies aren't properly maintained between the frontend and backend

## Solution

This plugin addresses these issues by:

1. Disabling admin access prevention for checkout and API requests
2. Modifying checkout URLs to include guest checkout parameters
3. Disabling checkout login requirements
4. Preventing admin redirects for checkout pages
5. Adding CORS headers for API requests
6. Providing custom REST API endpoints for guest checkout

## Installation

### Option 1: Full Plugin (Recommended)

1. Upload the entire `ankkor-woo-guest-checkout` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. The plugin will automatically configure <PERSON>oo<PERSON>ommer<PERSON> for guest checkout

### Option 2: Simple Plugin

If you prefer a simpler solution:

1. Upload the `force-guest-checkout-simple.php` file to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress

## Configuration

The plugin works automatically without any configuration needed. However, we recommend checking these WooCommerce settings:

1. Go to WooCommerce > Settings > Accounts & Privacy
2. Ensure "Allow customers to place orders without an account" is checked
3. Click "Save changes"

## Frontend Integration

Update your frontend code to work with the plugin:

```typescript
// In your cart store or checkout function
async function proceedToCheckout() {
  const baseUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL;
  
  // First try to get a guest checkout URL from our custom endpoint
  try {
    const guestCheckoutResponse = await fetch(`${baseUrl}/wp-json/ankkor/v1/guest-checkout`, {
      method: 'GET',
      credentials: 'include', // Include cookies for session handling
    });
    
    if (guestCheckoutResponse.ok) {
      const guestCheckoutData = await guestCheckoutResponse.json();
      if (guestCheckoutData.success && guestCheckoutData.checkout_url) {
        // Redirect to the checkout URL
        window.location.href = guestCheckoutData.checkout_url;
        return;
      }
    }
  } catch (error) {
    console.warn('Failed to get guest checkout URL from custom endpoint:', error);
  }
  
  // Fallback to direct checkout URL
  window.location.href = `${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`;
}
```

## API Endpoints

The plugin adds these custom REST API endpoints:

### 1. Get Guest Checkout URL

```
GET /wp-json/ankkor/v1/guest-checkout
```

Returns a properly formatted checkout URL with all necessary guest checkout parameters.

### 2. Fix Checkout Session

```
POST /wp-json/ankkor/v1/fix-checkout
```

Forces the WooCommerce session to use guest checkout mode.

## Troubleshooting

If you're still experiencing issues:

1. **Clear all caches**:
   - WordPress cache
   - Browser cache and cookies
   - WooCommerce cache
   - CDN cache

2. **Check for plugin conflicts**:
   - Temporarily deactivate other plugins that might affect checkout
   - Especially security plugins, membership plugins, and checkout customization plugins

3. **Test in incognito mode**:
   - Use a private/incognito browser window to avoid session conflicts

4. **Check server logs**:
   - WordPress debug log
   - WooCommerce logs (WooCommerce > Status > Logs)
   - Server error logs

## Support

For support, please create an issue on our GitHub repository or contact <NAME_EMAIL>. 
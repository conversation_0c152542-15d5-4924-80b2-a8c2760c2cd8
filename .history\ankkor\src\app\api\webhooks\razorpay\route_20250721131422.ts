import { NextRequest, NextResponse } from 'next/server';
import { GraphQLClient, gql } from 'graphql-request';
import crypto from 'crypto';

// Initialize GraphQL client for WooCommerce
const endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || 'https://your-wordpress-site.com/graphql';
const graphQLClient = new GraphQLClient(endpoint);

// Razorpay webhook secret
const RAZORPAY_WEBHOOK_SECRET = process.env.RAZORPAY_WEBHOOK_SECRET;

// Update order mutation
const UPDATE_ORDER_MUTATION = gql`
  mutation UpdateOrder($input: UpdateOrderInput!) {
    updateOrder(input: $input) {
      clientMutationId
      order {
        id
        databaseId
        status
      }
    }
  }
`;

// Get order by meta query
const GET_ORDER_BY_RAZORPAY_ID = gql`
  query GetOrderByRazorpayId($metaKey: String!, $metaValue: String!) {
    orders(where: {
      metaQuery: {
        metaArray: [
          {
            key: $metaKey,
            value: $metaValue,
            compareOperator: EQUAL_TO
          }
        ]
      },
      orderby: [{ field: DATE, order: DESC }],
      first: 1
    }) {
      nodes {
        id
        databaseId
        orderNumber
        status
        total
      }
    }
  }
`;

export async function POST(request: NextRequest) {
  try {
    // Get request body as text for signature verification
    const rawBody = await request.text();
    const body = JSON.parse(rawBody);
    
    // Get Razorpay signature from headers
    const razorpaySignature = request.headers.get('x-razorpay-signature');
    
    if (!razorpaySignature) {
      return NextResponse.json(
        { success: false, message: 'Missing Razorpay signature' },
        { status: 400 }
      );
    }
    
    if (!RAZORPAY_WEBHOOK_SECRET) {
      return NextResponse.json(
        { success: false, message: 'Webhook secret not configured' },
        { status: 500 }
      );
    }
    
    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', RAZORPAY_WEBHOOK_SECRET)
      .update(rawBody)
      .digest('hex');
    
    const isSignatureValid = expectedSignature === razorpaySignature;
    
    if (!isSignatureValid) {
      return NextResponse.json(
        { success: false, message: 'Invalid webhook signature' },
        { status: 401 }
      );
    }
    
    // Process webhook event
    const { event, payload } = body;
    
    // Log webhook event for debugging
    console.log(`Received Razorpay webhook: ${event}`, payload);
    
    // Handle different event types
    switch (event) {
      case 'payment.authorized':
      case 'payment.captured': {
        // Payment was successful, update the order
        const { payment } = payload;
        const razorpayOrderId = payment.order_id;
        const razorpayPaymentId = payment.id;
        
        // Find the WooCommerce order associated with this Razorpay order
        const orderResponse = await graphQLClient.request<{
          orders: {
            nodes: Array<{
              id: string;
              databaseId: number;
              orderNumber: string;
              status: string;
            }>;
          };
        }>(GET_ORDER_BY_RAZORPAY_ID, {
          metaKey: '_razorpay_order_id',
          metaValue: razorpayOrderId
        });
        
        const order = orderResponse.orders.nodes[0];
        
        if (!order) {
          return NextResponse.json(
            { success: false, message: 'No matching WooCommerce order found' },
            { status: 404 }
          );
        }
        
        // Update the order status to processing (payment completed)
        await graphQLClient.request(UPDATE_ORDER_MUTATION, {
          input: {
            clientMutationId: 'webhook_update_order',
            id: order.id,
            status: 'PROCESSING',
            metaData: [
              { key: '_razorpay_payment_id', value: razorpayPaymentId },
              { key: '_payment_transaction_id', value: razorpayPaymentId },
              { key: '_payment_status', value: 'completed' }
            ]
          }
        });
        
        return NextResponse.json({
          success: true,
          message: `Order ${order.orderNumber} updated to processing`
        });
      }
      
      case 'payment.failed': {
        // Payment failed, update the order status
        const { payment } = payload;
        const razorpayOrderId = payment.order_id;
        const razorpayPaymentId = payment.id;
        
        // Find the WooCommerce order associated with this Razorpay order
        const orderResponse = await graphQLClient.request<{
          orders: {
            nodes: Array<{
              id: string;
              databaseId: number;
              orderNumber: string;
              status: string;
            }>;
          };
        }>(GET_ORDER_BY_RAZORPAY_ID, {
          metaKey: '_razorpay_order_id',
          metaValue: razorpayOrderId
        });
        
        const order = orderResponse.orders.nodes[0];
        
        if (!order) {
          return NextResponse.json(
            { success: false, message: 'No matching WooCommerce order found' },
            { status: 404 }
          );
        }
        
        // Update the order status to failed
        await graphQLClient.request(UPDATE_ORDER_MUTATION, {
          input: {
            clientMutationId: 'webhook_update_order',
            id: order.id,
            status: 'FAILED',
            metaData: [
              { key: '_razorpay_payment_id', value: razorpayPaymentId },
              { key: '_payment_status', value: 'failed' },
              { key: '_payment_failure_reason', value: payment.error_description || 'Payment failed' }
            ]
          }
        });
        
        return NextResponse.json({
          success: true,
          message: `Order ${order.orderNumber} updated to failed`
        });
      }
      
      default:
        // Acknowledge other events but don't process them
        return NextResponse.json({
          success: true,
          message: `Event ${event} acknowledged but not processed`
        });
    }
    
  } catch (error) {
    console.error('Error processing Razorpay webhook:', error);
    
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
} 